{"name": "flowdownload", "private": true, "version": "1.0.0", "description": "Modern, clean, sophisticated desktop downloader app", "type": "module", "scripts": {"dev": "npm run config:generate && vite", "build": "npm run config:generate && tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "npm run config:generate && tauri dev", "tauri:build": "npm run config:generate && tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write .", "config:generate": "node scripts/generate-tauri-config.js", "test:config": "npm run config:generate && echo 'Configuration test completed'", "test:domain": "vitest run src/__tests__/domain", "test:infrastructure": "vitest run src/__tests__/infrastructure", "test:migration": "vitest run src/__tests__/migration", "test:application": "vitest run src/__tests__/application"}, "dependencies": {"@tauri-apps/api": ">=2.0.0", "@tauri-apps/plugin-dialog": ">=2.0.0", "@tauri-apps/plugin-fs": ">=2.0.0", "@tauri-apps/plugin-shell": ">=2.0.0", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-toastify": "^9.1.3", "zustand": "^4.4.1"}, "devDependencies": {"@tauri-apps/cli": ">=2.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.29", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^3.2.0", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^26.1.0", "postcss": "^8.4.27", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.5.4", "vite": "^4.4.4", "vite-tsconfig-paths": "^4.2.0", "vitest": "^3.2.0"}, "license": "MIT"}