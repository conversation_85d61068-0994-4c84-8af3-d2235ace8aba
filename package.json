{"name": "flowdownload", "private": true, "version": "1.0.0", "description": "Modern, clean, sophisticated desktop downloader app", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 20", "format": "prettier --write ."}, "dependencies": {"@tauri-apps/api": ">=2.0.0", "@tauri-apps/plugin-shell": ">=2.0.0", "@tauri-apps/plugin-dialog": ">=2.0.0", "@tauri-apps/plugin-fs": ">=2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.1", "react-toastify": "^9.1.3"}, "devDependencies": {"@tauri-apps/cli": ">=2.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.4.4", "vite-tsconfig-paths": "^4.2.0"}, "license": "MIT"}