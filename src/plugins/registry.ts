// Plugin registry implementation for FlowDownload

import { 
  Plugin, 
  PluginInstance, 
  PluginManifest, 
  PluginRegistry as IPluginRegistry,
  PluginPackage,
  PluginSearchResult,
  PluginAPI
} from './types';
import { PluginError } from './errors';

export class PluginRegistry implements IPluginRegistry {
  public plugins: Map<string, Plugin> = new Map();
  private api: PluginAPI;

  constructor(api: PluginAPI) {
    this.api = api;
  }

  async install(pluginPackage: PluginPackage): Promise<void> {
    const { manifest, code } = pluginPackage;
    
    // Validate manifest
    this.validateManifest(manifest);
    
    // Check if plugin already exists
    if (this.plugins.has(manifest.name)) {
      throw new PluginError(`Plugin ${manifest.name} is already installed`, {
        code: 'PLUGIN_ALREADY_EXISTS',
        plugin: manifest.name
      });
    }

    try {
      // Create plugin instance
      const instance = await this.createPluginInstance(code, manifest);
      
      // Create plugin object
      const plugin: Plugin = {
        manifest,
        instance,
        enabled: false,
        installed: true
      };

      // Store plugin
      this.plugins.set(manifest.name, plugin);

      // Run install hook
      if (instance.onInstall) {
        await instance.onInstall();
      }

      console.log(`Plugin ${manifest.name} installed successfully`);
    } catch (error) {
      throw new PluginError(`Failed to install plugin ${manifest.name}: ${error}`, {
        code: 'PLUGIN_INSTALL_FAILED',
        plugin: manifest.name,
        details: { originalError: error }
      });
    }
  }

  async uninstall(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new PluginError(`Plugin ${pluginName} is not installed`, {
        code: 'PLUGIN_NOT_FOUND',
        plugin: pluginName
      });
    }

    try {
      // Disable first if enabled
      if (plugin.enabled) {
        await this.disable(pluginName);
      }

      // Run uninstall hook
      if (plugin.instance.onUninstall) {
        await plugin.instance.onUninstall();
      }

      // Remove from registry
      this.plugins.delete(pluginName);

      console.log(`Plugin ${pluginName} uninstalled successfully`);
    } catch (error) {
      throw new PluginError(`Failed to uninstall plugin ${pluginName}: ${error}`, {
        code: 'PLUGIN_UNINSTALL_FAILED',
        plugin: pluginName,
        details: { originalError: error }
      });
    }
  }

  async enable(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new PluginError(`Plugin ${pluginName} is not installed`, {
        code: 'PLUGIN_NOT_FOUND',
        plugin: pluginName
      });
    }

    if (plugin.enabled) {
      return; // Already enabled
    }

    try {
      // Run enable hook
      if (plugin.instance.onEnable) {
        await plugin.instance.onEnable();
      }

      plugin.enabled = true;
      console.log(`Plugin ${pluginName} enabled successfully`);
    } catch (error) {
      throw new PluginError(`Failed to enable plugin ${pluginName}: ${error}`, {
        code: 'PLUGIN_ENABLE_FAILED',
        plugin: pluginName,
        details: { originalError: error }
      });
    }
  }

  async disable(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new PluginError(`Plugin ${pluginName} is not installed`, {
        code: 'PLUGIN_NOT_FOUND',
        plugin: pluginName
      });
    }

    if (!plugin.enabled) {
      return; // Already disabled
    }

    try {
      // Run disable hook
      if (plugin.instance.onDisable) {
        await plugin.instance.onDisable();
      }

      plugin.enabled = false;
      console.log(`Plugin ${pluginName} disabled successfully`);
    } catch (error) {
      throw new PluginError(`Failed to disable plugin ${pluginName}: ${error}`, {
        code: 'PLUGIN_DISABLE_FAILED',
        plugin: pluginName,
        details: { originalError: error }
      });
    }
  }

  getPlugin(name: string): Plugin | undefined {
    return this.plugins.get(name);
  }

  getEnabledPlugins(): Plugin[] {
    return Array.from(this.plugins.values()).filter(plugin => plugin.enabled);
  }

  async getPluginsForUrl(url: string): Promise<Plugin[]> {
    const enabledPlugins = this.getEnabledPlugins();
    const supportingPlugins: Plugin[] = [];

    for (const plugin of enabledPlugins) {
      try {
        const supports = await plugin.instance.supports(url);
        if (supports) {
          supportingPlugins.push(plugin);
        }
      } catch (error) {
        console.warn(`Error checking plugin ${plugin.manifest.name} support for URL: ${error}`);
      }
    }

    return supportingPlugins;
  }

  async searchPlugins(query: string): Promise<PluginSearchResult[]> {
    // This would typically connect to a plugin marketplace API
    // For now, return mock results
    return [
      {
        name: 'enhanced-youtube',
        version: '1.2.0',
        author: 'FlowDownload Team',
        description: 'Enhanced YouTube downloader with playlist support',
        downloads: 15420,
        rating: 4.8,
        tags: ['youtube', 'playlist', 'music'],
        lastUpdated: new Date('2024-01-15')
      },
      {
        name: 'social-media-pack',
        version: '2.1.0',
        author: 'Community',
        description: 'Complete social media platform support package',
        downloads: 8930,
        rating: 4.6,
        tags: ['social', 'instagram', 'tiktok', 'twitter'],
        lastUpdated: new Date('2024-01-10')
      }
    ].filter(plugin => 
      plugin.name.toLowerCase().includes(query.toLowerCase()) ||
      plugin.description.toLowerCase().includes(query.toLowerCase()) ||
      plugin.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
  }

  private validateManifest(manifest: PluginManifest): void {
    const requiredFields = ['name', 'version', 'author', 'description', 'main'];
    
    for (const field of requiredFields) {
      if (!manifest[field as keyof PluginManifest]) {
        throw new PluginError(`Missing required field: ${field}`, {
          code: 'INVALID_MANIFEST',
          plugin: manifest.name || 'unknown'
        });
      }
    }

    // Validate version format (basic semver check)
    if (!/^\d+\.\d+\.\d+/.test(manifest.version)) {
      throw new PluginError('Invalid version format. Use semantic versioning (e.g., 1.0.0)', {
        code: 'INVALID_VERSION',
        plugin: manifest.name
      });
    }
  }

  private async createPluginInstance(code: string, manifest: PluginManifest): Promise<PluginInstance> {
    try {
      // Create a secure execution context for the plugin
      const pluginContext = this.createPluginContext(manifest);
      
      // Execute plugin code in isolated context
      const pluginFunction = new Function('api', 'require', 'exports', 'module', code);
      const exports = {};
      const module = { exports };
      
      // Mock require function for basic modules
      const require = (moduleName: string) => {
        // Only allow specific safe modules
        const allowedModules = ['url', 'path', 'querystring'];
        if (!allowedModules.includes(moduleName)) {
          throw new Error(`Module ${moduleName} is not allowed`);
        }
        
        // Return mock implementations or real modules based on security policy
        switch (moduleName) {
          case 'url':
            return { URL, URLSearchParams };
          case 'path':
            return { basename: (p: string) => p.split('/').pop(), extname: (p: string) => p.split('.').pop() };
          default:
            throw new Error(`Module ${moduleName} is not implemented`);
        }
      };

      pluginFunction.call(null, pluginContext, require, exports, module);

      // Extract the plugin instance
      const instance = (module.exports as any).default || module.exports;
      
      if (!instance || typeof instance !== 'object') {
        throw new Error('Plugin must export a default object or export an object');
      }

      // Validate required methods
      if (typeof instance.supports !== 'function') {
        throw new Error('Plugin must implement supports() method');
      }

      if (typeof instance.extract !== 'function') {
        throw new Error('Plugin must implement extract() method');
      }

      return instance;
    } catch (error) {
      throw new PluginError(`Failed to create plugin instance: ${error}`, {
        code: 'PLUGIN_CREATION_FAILED',
        plugin: manifest.name,
        details: { originalError: error }
      });
    }
  }

  private createPluginContext(manifest: PluginManifest): PluginAPI {
    // Create a restricted API context for the plugin
    return {
      download: {
        create: async (url: string, options = {}) => {
          // Implementation would call the main download system
          return this.api.download.create(url, options);
        },
        pause: async (id: string) => {
          return this.api.download.pause(id);
        },
        resume: async (id: string) => {
          return this.api.download.resume(id);
        },
        cancel: async (id: string) => {
          return this.api.download.cancel(id);
        },
        getStatus: async (id: string) => {
          return this.api.download.getStatus(id);
        }
      },
      
      upload: {
        create: async (file, platform, options = {}) => {
          return this.api.upload.create(file, platform, options);
        },
        getStatus: async (id: string) => {
          return this.api.upload.getStatus(id);
        }
      },
      
      storage: {
        get: async (key: string) => {
          // Namespace storage per plugin
          const namespacedKey = `plugin_${manifest.name}_${key}`;
          return this.api.storage.get(namespacedKey);
        },
        set: async (key: string, value: any) => {
          const namespacedKey = `plugin_${manifest.name}_${key}`;
          return this.api.storage.set(namespacedKey, value);
        },
        delete: async (key: string) => {
          const namespacedKey = `plugin_${manifest.name}_${key}`;
          return this.api.storage.delete(namespacedKey);
        },
        clear: async () => {
          // Clear only this plugin's storage
          const keys = await this.api.storage.get('__all_keys__') || [];
          const pluginKeys = keys.filter((key: string) => key.startsWith(`plugin_${manifest.name}_`));
          for (const key of pluginKeys) {
            await this.api.storage.delete(key);
          }
        }
      },
      
      ui: {
        showNotification: (message: string, type = 'info' as const) => {
          this.api.ui.showNotification(`[${manifest.name}] ${message}`, type);
        },
        showModal: async (component: React.ComponentType) => {
          return this.api.ui.showModal(component);
        },
        addMenuItem: (item) => {
          // Namespace menu item ID
          const namespacedItem = {
            ...item,
            id: `plugin_${manifest.name}_${item.id}`
          };
          this.api.ui.addMenuItem(namespacedItem);
        },
        removeMenuItem: (id: string) => {
          const namespacedId = `plugin_${manifest.name}_${id}`;
          this.api.ui.removeMenuItem(namespacedId);
        }
      },
      
      system: this.api.system,
      
      network: {
        fetch: async (url: string, options = {}) => {
          // Add plugin identification to requests
          const headers = {
            'User-Agent': `FlowDownload-Plugin/${manifest.name}/${manifest.version}`,
            ...options.headers
          };
          return this.api.network.fetch(url, { ...options, headers });
        },
        isOnline: () => this.api.network.isOnline()
      }
    };
  }
}

export { PluginError } from './errors';