// Plugin system type definitions for FlowDownload

export interface PluginManifest {
  name: string;
  version: string;
  author: string;
  description: string;
  homepage?: string;
  license: string;
  keywords: string[];
  main: string; // Entry point file
  permissions: PluginPermission[];
  dependencies?: Record<string, string>;
  platforms: Platform[];
}

export interface PluginPermission {
  type: 'network' | 'filesystem' | 'shell' | 'notifications';
  description: string;
  required: boolean;
}

export interface Plugin {
  manifest: PluginManifest;
  instance: PluginInstance;
  enabled: boolean;
  installed: boolean;
}

export interface PluginInstance {
  // Core plugin interface
  supports(url: string): Promise<boolean>;
  extract(url: string): Promise<MediaInfo>;
  download?(info: MediaInfo): Promise<DownloadStream>;
  upload?(media: MediaFile, platform: Platform): Promise<UploadResult>;
  
  // Lifecycle hooks
  onInstall?(): Promise<void>;
  onUninstall?(): Promise<void>;
  onEnable?(): Promise<void>;
  onDisable?(): Promise<void>;
  
  // Configuration
  getConfig?(): PluginConfig;
  setConfig?(config: Partial<PluginConfig>): Promise<void>;
  
  // UI hooks
  getSettingsComponent?(): React.ComponentType<PluginSettingsProps>;
  getMenuItems?(): PluginMenuItem[];
}

export interface MediaInfo {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  duration?: number;
  formats: MediaFormat[];
  subtitles?: SubtitleTrack[];
  metadata: Record<string, any>;
}

export interface MediaFormat {
  id: string;
  quality: string;
  format: string;
  url: string;
  filesize?: number;
  bitrate?: number;
  resolution?: string;
  fps?: number;
}

export interface SubtitleTrack {
  language: string;
  label: string;
  url: string;
  format: 'srt' | 'vtt' | 'ass';
}

export interface DownloadStream {
  stream: ReadableStream<Uint8Array>;
  totalSize?: number;
  contentType?: string;
  filename?: string;
}

export interface MediaFile {
  path: string;
  name: string;
  size: number;
  type: string;
  duration?: number;
  resolution?: string;
  metadata: Record<string, any>;
}

export interface UploadResult {
  platform: Platform;
  url: string;
  id: string;
  status: 'success' | 'failed' | 'processing';
  message?: string;
}

export interface PluginConfig {
  [key: string]: any;
}

export interface PluginSettingsProps {
  config: PluginConfig;
  onConfigChange: (config: Partial<PluginConfig>) => void;
}

export interface PluginMenuItem {
  id: string;
  label: string;
  icon?: string;
  action: () => void;
  separator?: boolean;
}

export type Platform = 
  | 'youtube' 
  | 'tiktok' 
  | 'instagram' 
  | 'twitter' 
  | 'facebook'
  | 'vimeo'
  | 'twitch'
  | 'reddit'
  | 'custom';

export interface PluginError extends Error {
  code: string;
  plugin: string;
  details?: Record<string, any>;
}

export interface PluginRegistry {
  plugins: Map<string, Plugin>;
  install(pluginPackage: PluginPackage): Promise<void>;
  uninstall(pluginName: string): Promise<void>;
  enable(pluginName: string): Promise<void>;
  disable(pluginName: string): Promise<void>;
  getPlugin(name: string): Plugin | undefined;
  getEnabledPlugins(): Plugin[];
  getPluginsForUrl(url: string): Promise<Plugin[]>;
  searchPlugins(query: string): Promise<PluginSearchResult[]>;
}

export interface PluginPackage {
  manifest: PluginManifest;
  code: string;
  assets?: Record<string, Uint8Array>;
}

export interface PluginSearchResult {
  name: string;
  version: string;
  author: string;
  description: string;
  downloads: number;
  rating: number;
  tags: string[];
  lastUpdated: Date;
}

// Plugin development utilities
export interface PluginAPI {
  // Core APIs
  download: {
    create(url: string, options?: DownloadOptions): Promise<string>;
    pause(id: string): Promise<void>;
    resume(id: string): Promise<void>;
    cancel(id: string): Promise<void>;
    getStatus(id: string): Promise<DownloadStatus>;
  };
  
  upload: {
    create(file: MediaFile, platform: Platform, options?: UploadOptions): Promise<string>;
    getStatus(id: string): Promise<UploadStatus>;
  };
  
  // Storage APIs
  storage: {
    get(key: string): Promise<any>;
    set(key: string, value: any): Promise<void>;
    delete(key: string): Promise<void>;
    clear(): Promise<void>;
  };
  
  // UI APIs
  ui: {
    showNotification(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
    showModal(component: React.ComponentType): Promise<any>;
    addMenuItem(item: PluginMenuItem): void;
    removeMenuItem(id: string): void;
  };
  
  // System APIs
  system: {
    getVersion(): string;
    getOS(): string;
    openExternal(url: string): Promise<void>;
    showInFolder(path: string): Promise<void>;
  };
  
  // Network APIs
  network: {
    fetch(url: string, options?: RequestInit): Promise<Response>;
    isOnline(): boolean;
  };
}

export interface DownloadOptions {
  quality?: string;
  format?: string;
  outputPath?: string;
  filename?: string;
  headers?: Record<string, string>;
}

export interface UploadOptions {
  title?: string;
  description?: string;
  tags?: string[];
  privacy?: 'public' | 'private' | 'unlisted';
  scheduled?: Date;
}

export interface DownloadStatus {
  id: string;
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'paused' | 'cancelled';
  progress: number;
  speed?: number;
  eta?: number;
  error?: string;
}

export interface UploadStatus {
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  url?: string;
  error?: string;
}