// Plugin Error class
export class PluginError extends Error {
  public code: string;
  public plugin: string;
  public details?: Record<string, any>;

  constructor(message: string, context: { code: string; plugin: string; details?: Record<string, any> }) {
    super(message);
    this.name = 'PluginError';
    this.code = context.code;
    this.plugin = context.plugin;
    this.details = context.details;
  }
}