import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { ConfigManager } from './configManager';
import { DEFAULT_CONFIG } from './constants';

// Mock Tauri APIs
const mockTauriFS = {
  readTextFile: vi.fn(),
  writeTextFile: vi.fn(),
  exists: vi.fn(),
  createDir: vi.fn(),
};

const mockTauriPath = {
  join: vi.fn(),
  appConfigDir: vi.fn(),
  downloadDir: vi.fn(),
};

// Mock Tauri modules
vi.mock('@tauri-apps/plugin-fs', () => mockTauriFS);
vi.mock('@tauri-apps/api/path', () => mockTauriPath);

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let originalTauri: any;
  let originalLocalStorage: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock localStorage
    originalLocalStorage = global.localStorage;
    global.localStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
      length: 0,
      key: vi.fn(),
    };

    // Mock window.__TAURI__
    originalTauri = (global as any).window?.__TAURI__;
    
    configManager = new ConfigManager();
  });

  afterEach(() => {
    // Restore original objects
    if (originalLocalStorage) {
      global.localStorage = originalLocalStorage;
    }
    if (originalTauri !== undefined) {
      (global as any).window = { ...(global as any).window, __TAURI__: originalTauri };
    }
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('should initialize successfully in web environment', async () => {
      // Ensure we're in web environment
      (global as any).window = {};
      
      // Mock localStorage returning null (no saved config)
      vi.mocked(global.localStorage.getItem).mockReturnValue(null);

      await configManager.initialize();
      
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
      expect(global.localStorage.getItem).toHaveBeenCalledWith('flowdownload-config');
    });

    it('should load configuration from localStorage in web environment', async () => {
      // Ensure we're in web environment
      (global as any).window = {};
      
      const savedConfig = {
        downloads: {
          maxConcurrent: 5,
          retryAttempts: 5,
        },
      };

      vi.mocked(global.localStorage.getItem).mockReturnValue(JSON.stringify(savedConfig));

      await configManager.initialize();
      
      const config = configManager.getConfig();
      expect(config.downloads.maxConcurrent).toBe(5);
      expect(config.downloads.retryAttempts).toBe(5);
      // Other values should remain default
      expect(config.security.validateUrls).toBe(DEFAULT_CONFIG.security.validateUrls);
    });

    it('should initialize successfully in Tauri environment', async () => {
      // Mock Tauri environment
      (global as any).window = { __TAURI__: {} };
      
      mockTauriPath.appConfigDir.mockResolvedValue('/app/config');
      mockTauriPath.join.mockResolvedValue('/app/config/config.json');
      mockTauriPath.downloadDir.mockResolvedValue('/Users/<USER>/Downloads');
      mockTauriFS.exists.mockResolvedValue(false);

      await configManager.initialize();
      
      const config = configManager.getConfig();
      expect(config).toBeDefined();
      expect(mockTauriPath.appConfigDir).toHaveBeenCalled();
      expect(mockTauriPath.join).toHaveBeenCalledWith('/app/config', 'config.json');
    });

    it('should load existing configuration file in Tauri environment', async () => {
      // Mock Tauri environment
      (global as any).window = { __TAURI__: {} };
      
      const savedConfig = {
        downloads: {
          maxConcurrent: 8,
          defaultPath: '/custom/downloads',
        },
      };

      mockTauriPath.appConfigDir.mockResolvedValue('/app/config');
      mockTauriPath.join.mockResolvedValue('/app/config/config.json');
      mockTauriPath.downloadDir.mockResolvedValue('/Users/<USER>/Downloads');
      mockTauriFS.exists.mockResolvedValue(true);
      mockTauriFS.readTextFile.mockResolvedValue(JSON.stringify(savedConfig));

      await configManager.initialize();
      
      const config = configManager.getConfig();
      expect(config.downloads.maxConcurrent).toBe(8);
      expect(config.downloads.defaultPath).toBe('/custom/downloads');
      expect(mockTauriFS.readTextFile).toHaveBeenCalledWith('/app/config/config.json');
    });

    it('should handle initialization errors gracefully', async () => {
      // Mock Tauri environment with error
      (global as any).window = { __TAURI__: {} };
      
      mockTauriPath.appConfigDir.mockRejectedValue(new Error('Path access denied'));

      await configManager.initialize();
      
      // Should still initialize with default config
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });
  });

  describe('Configuration Access', () => {
    beforeEach(async () => {
      // Initialize with default config
      (global as any).window = {};
      vi.mocked(global.localStorage.getItem).mockReturnValue(null);
      await configManager.initialize();
    });

    it('should get specific configuration values', () => {
      const downloadsConfig = configManager.get('downloads');
      expect(downloadsConfig).toEqual(DEFAULT_CONFIG.downloads);
      
      const securityConfig = configManager.get('security');
      expect(securityConfig).toEqual(DEFAULT_CONFIG.security);
    });

    it('should return a copy of configuration to prevent mutations', () => {
      const config1 = configManager.getConfig();
      const config2 = configManager.getConfig();
      
      // Should be different objects
      expect(config1).not.toBe(config2);
      
      // But with same content
      expect(config1).toEqual(config2);
      
      // Modifying one shouldn't affect the other
      config1.downloads.maxConcurrent = 99;
      expect(config2.downloads.maxConcurrent).toBe(DEFAULT_CONFIG.downloads.maxConcurrent);
    });
  });

  describe('Configuration Updates', () => {
    beforeEach(async () => {
      // Initialize in web environment
      (global as any).window = {};
      vi.mocked(global.localStorage.getItem).mockReturnValue(null);
      await configManager.initialize();
    });

    it('should update configuration partially', async () => {
      const updates = {
        downloads: {
          maxConcurrent: 10,
        },
      };

      await configManager.updateConfig(updates);
      
      const config = configManager.getConfig();
      expect(config.downloads.maxConcurrent).toBe(10);
      // Other download settings should remain unchanged
      expect(config.downloads.retryAttempts).toBe(DEFAULT_CONFIG.downloads.retryAttempts);
      // Other sections should remain unchanged
      expect(config.security).toEqual(DEFAULT_CONFIG.security);
    });

    it('should deep merge nested configuration updates', async () => {
      const updates = {
        security: {
          validateUrls: false,
        },
        downloads: {
          maxConcurrent: 7,
        },
      };

      await configManager.updateConfig(updates);
      
      const config = configManager.getConfig();
      expect(config.security.validateUrls).toBe(false);
      expect(config.security.allowedDomains).toEqual(DEFAULT_CONFIG.security.allowedDomains);
      expect(config.downloads.maxConcurrent).toBe(7);
      expect(config.downloads.retryAttempts).toBe(DEFAULT_CONFIG.downloads.retryAttempts);
    });

    it('should save to localStorage in web environment', async () => {
      const updates = {
        downloads: {
          maxConcurrent: 6,
        },
      };

      await configManager.updateConfig(updates);
      
      expect(global.localStorage.setItem).toHaveBeenCalledWith(
        'flowdownload-config',
        expect.stringContaining('"maxConcurrent":6')
      );
    });

    it('should save to file in Tauri environment', async () => {
      // Switch to Tauri environment
      (global as any).window = { __TAURI__: {} };
      
      // Reinitialize in Tauri environment
      const tauriConfigManager = new ConfigManager();
      mockTauriPath.appConfigDir.mockResolvedValue('/app/config');
      mockTauriPath.join.mockResolvedValue('/app/config/config.json');
      mockTauriPath.downloadDir.mockResolvedValue('/Users/<USER>/Downloads');
      mockTauriFS.exists.mockResolvedValue(false);
      mockTauriFS.createDir.mockResolvedValue(undefined);
      mockTauriFS.writeTextFile.mockResolvedValue(undefined);
      
      await tauriConfigManager.initialize();

      const updates = {
        downloads: {
          maxConcurrent: 12,
          retryAttempts: 3,
          timeoutSeconds: 30,
          chunkSizeBytes: 1024 * 1024,
          defaultQuality: 'auto',
          supportedFormats: ['mp4', 'mp3', 'webm'],
          progressUpdateInterval: 500
        },
      };

      await tauriConfigManager.updateConfig(updates);
      
      expect(mockTauriFS.writeTextFile).toHaveBeenCalledWith(
        '/app/config/config.json',
        expect.stringContaining('"maxConcurrent":12')
      );
    });
  });

  describe('Configuration Reset', () => {
    beforeEach(async () => {
      // Initialize in web environment
      (global as any).window = {};
      vi.mocked(global.localStorage.getItem).mockReturnValue(null);
      await configManager.initialize();
    });

    it('should reset configuration to defaults', async () => {
      // First modify the configuration
      await configManager.updateConfig({
        downloads: { 
          maxConcurrent: 15,
          retryAttempts: 3,
          timeoutSeconds: 30,
          chunkSizeBytes: 1024 * 1024,
          defaultQuality: 'auto',
          supportedFormats: ['mp4', 'mp3', 'webm'],
          progressUpdateInterval: 500
        },
        security: { 
          allowedDomains: ['youtube.com', 'vimeo.com'],
          maxFileSize: 5 * 1024 * 1024 * 1024,
          sanitizeFilenames: true,
          validateUrls: false
        },
      });

      // Verify it was changed
      let config = configManager.getConfig();
      expect(config.downloads.maxConcurrent).toBe(15);
      expect(config.security.validateUrls).toBe(false);

      // Reset to defaults
      await configManager.resetConfig();

      // Verify it's back to defaults
      config = configManager.getConfig();
      expect(config.downloads.maxConcurrent).toBe(DEFAULT_CONFIG.downloads.maxConcurrent);
      expect(config.security.validateUrls).toBe(DEFAULT_CONFIG.security.validateUrls);
    });

    it('should save reset configuration', async () => {
      await configManager.resetConfig();
      
      expect(global.localStorage.setItem).toHaveBeenCalledWith(
        'flowdownload-config',
        JSON.stringify(DEFAULT_CONFIG, null, 2)
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle JSON parse errors in localStorage gracefully', async () => {
      // Ensure we're in web environment
      (global as any).window = {};
      
      vi.mocked(global.localStorage.getItem).mockReturnValue('invalid json{');

      await configManager.initialize();
      
      // Should fall back to default config
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('should handle file read errors in Tauri environment gracefully', async () => {
      // Mock Tauri environment
      (global as any).window = { __TAURI__: {} };
      
      mockTauriPath.appConfigDir.mockResolvedValue('/app/config');
      mockTauriPath.join.mockResolvedValue('/app/config/config.json');
      mockTauriPath.downloadDir.mockResolvedValue('/Users/<USER>/Downloads');
      mockTauriFS.exists.mockResolvedValue(true);
      mockTauriFS.readTextFile.mockRejectedValue(new Error('Permission denied'));

      await configManager.initialize();
      
      // Should fall back to default config
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('should handle save errors gracefully', async () => {
      // Initialize in web environment
      (global as any).window = {};
      vi.mocked(global.localStorage.getItem).mockReturnValue(null);
      await configManager.initialize();

      // Mock localStorage.setItem to throw an error
      vi.mocked(global.localStorage.setItem).mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // Should not throw an error
      await expect(configManager.updateConfig({
        downloads: { 
          maxConcurrent: 5,
          retryAttempts: 3,
          timeoutSeconds: 30,
          chunkSizeBytes: 1024 * 1024,
          defaultQuality: 'auto',
          supportedFormats: ['mp4', 'mp3', 'webm'],
          progressUpdateInterval: 500
        }
      })).resolves.not.toThrow();
    });
  });
});
