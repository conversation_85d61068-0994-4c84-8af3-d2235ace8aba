// Configuration Manager
// Handles loading, saving, and merging of application configuration

import { AppConfig, DEFAULT_CONFIG } from './constants';

export class ConfigManager {
  private config: AppConfig;
  private configPath: string = '';
  private isInitialized: boolean = false;

  constructor() {
    this.config = { ...DEFAULT_CONFIG };
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Set dynamic paths based on environment
      await this.setDynamicPaths();
      
      // Load configuration from file if it exists
      await this.loadConfig();
      
      this.isInitialized = true;
      console.log('Configuration manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize configuration manager:', error);
      // Use default config if initialization fails
      this.config = { ...DEFAULT_CONFIG };
      this.isInitialized = true;
    }
  }

  /**
   * Get the current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Get a specific configuration value
   */
  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key];
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    this.config = this.mergeConfig(this.config, updates);
    await this.saveConfig();
  }

  /**
   * Reset configuration to defaults
   */
  async resetConfig(): Promise<void> {
    this.config = { ...DEFAULT_CONFIG };
    await this.setDynamicPaths();
    await this.saveConfig();
  }

  /**
   * Load configuration from file
   */
  private async loadConfig(): Promise<void> {
    try {
      // Check if we're in Tauri environment
      if (window.__TAURI__) {
        const { readTextFile, exists } = await import('@tauri-apps/plugin-fs');
        const { join, appConfigDir } = await import('@tauri-apps/api/path');
        
        const configDir = await appConfigDir();
        this.configPath = await join(configDir, 'config.json');
        
        if (await exists(this.configPath)) {
          const configText = await readTextFile(this.configPath);
          const loadedConfig = JSON.parse(configText) as Partial<AppConfig>;
          this.config = this.mergeConfig(DEFAULT_CONFIG, loadedConfig);
          console.log('Configuration loaded from:', this.configPath);
        } else {
          console.log('No existing configuration found, using defaults');
        }
      } else {
        // Web environment - use localStorage
        const savedConfig = localStorage.getItem('flowdownload-config');
        if (savedConfig) {
          const loadedConfig = JSON.parse(savedConfig) as Partial<AppConfig>;
          this.config = this.mergeConfig(DEFAULT_CONFIG, loadedConfig);
          console.log('Configuration loaded from localStorage');
        }
      }
    } catch (error) {
      console.warn('Failed to load configuration, using defaults:', error);
      this.config = { ...DEFAULT_CONFIG };
    }
  }

  /**
   * Save configuration to file
   */
  private async saveConfig(): Promise<void> {
    try {
      if (window.__TAURI__) {
        const { writeTextFile, create } = await import('@tauri-apps/plugin-fs');
        const { dirname } = await import('@tauri-apps/api/path');
        
        if (this.configPath) {
          // Ensure config directory exists
          const configDir = await dirname(this.configPath);
          try {
            await create(configDir);
          } catch {
            // Directory might already exist, ignore error
          }
          
          // Save configuration
          await writeTextFile(this.configPath, JSON.stringify(this.config, null, 2));
          console.log('Configuration saved to:', this.configPath);
        }
      } else {
        // Web environment - use localStorage
        localStorage.setItem('flowdownload-config', JSON.stringify(this.config));
        console.log('Configuration saved to localStorage');
      }
    } catch (error) {
      console.error('Failed to save configuration:', error);
    }
  }

  /**
   * Set dynamic paths based on the current environment
   */
  private async setDynamicPaths(): Promise<void> {
    try {
      if (window.__TAURI__) {
        const { 
          downloadDir, 
          appConfigDir, 
          appLogDir, 
          tempDir,
          appDataDir 
        } = await import('@tauri-apps/api/path');
        const { join } = await import('@tauri-apps/api/path');

        // Set platform-specific paths
        this.config.paths.defaultDownloadDir = await downloadDir() || '';
        this.config.paths.configDir = await appConfigDir() || '';
        this.config.paths.logDir = await appLogDir() || '';
        this.config.paths.tempDir = await tempDir() || '';
        this.config.paths.pluginDir = await join(await appDataDir() || '', 'plugins');
      } else {
        // Web environment fallbacks
        this.config.paths.defaultDownloadDir = 'Downloads';
        this.config.paths.configDir = '';
        this.config.paths.logDir = '';
        this.config.paths.tempDir = '';
        this.config.paths.pluginDir = '';
      }
    } catch (error) {
      console.warn('Failed to set dynamic paths:', error);
    }
  }

  /**
   * Deep merge two configuration objects
   */
  private mergeConfig(base: AppConfig, updates: Partial<AppConfig>): AppConfig {
    const result = { ...base };

    for (const key in updates) {
      const updateValue = updates[key as keyof AppConfig];
      if (updateValue !== undefined) {
        if (typeof updateValue === 'object' && updateValue !== null && !Array.isArray(updateValue)) {
          // Type-safe merge for nested objects
          const currentValue = (result as Record<string, unknown>)[key];
          if (typeof currentValue === 'object' && currentValue !== null) {
            (result as Record<string, unknown>)[key] = {
              ...(currentValue as Record<string, unknown>),
              ...(updateValue as Record<string, unknown>)
            };
          } else {
            (result as Record<string, unknown>)[key] = updateValue;
          }
        } else {
          (result as Record<string, unknown>)[key] = updateValue;
        }
      }
    }

    return result;
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): { isDev: boolean; isProduction: boolean; isTauri: boolean } {
    return {
      isDev: process.env.NODE_ENV === 'development',
      isProduction: process.env.NODE_ENV === 'production',
      isTauri: !!window.__TAURI__,
    };
  }
}

// Global configuration manager instance
export const configManager = new ConfigManager();

// Helper functions for easy access
export const getConfig = () => configManager.getConfig();
export const updateConfig = (updates: Partial<AppConfig>) => configManager.updateConfig(updates);
export const resetConfig = () => configManager.resetConfig();

// Initialize configuration on module load
if (typeof window !== 'undefined') {
  configManager.initialize().catch(console.error);
}
