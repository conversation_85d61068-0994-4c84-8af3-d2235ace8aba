import { useDownloadStore } from '../store/downloadStore';

// Enhanced demo function to simulate realistic download phases
export const simulateDownload = (downloadId: string) => {
  const { updateDownload } = useDownloadStore.getState();
  
  // Phase 1: Extract video information (1-2 seconds)
  updateDownload(downloadId, { 
    status: 'downloading', 
    progress: 0,
    downloadSpeed: undefined,
    fileSize: 'Calculating...'
  });

  // Simulate extracting video info
  setTimeout(() => {
    updateDownload(downloadId, { 
      progress: 5,
      fileSize: 'Extracting video information...'
    });
  }, 500);

  // Phase 2: Selecting best format
  setTimeout(() => {
    updateDownload(downloadId, { 
      progress: 15,
      fileSize: 'Selecting optimal format...'
    });
  }, 1000);

  // Phase 3: Start actual download with file size
  setTimeout(() => {
    const fileSizes = ['856 MB', '1.2 GB', '2.3 GB', '1.8 GB', '3.1 GB'];
    const randomSize = fileSizes[Math.floor(Math.random() * fileSizes.length)];
    
    updateDownload(downloadId, { 
      progress: 25,
      downloadSpeed: '8.4 MB/s',
      fileSize: randomSize
    });

    // Continue with faster download simulation
    let progress = 25;
    const downloadInterval = setInterval(() => {
      progress += Math.random() * 8 + 5; // Faster progress increment (5-13% per update)
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(downloadInterval);
        updateDownload(downloadId, { 
          status: 'completed', 
          progress: 100,
          downloadSpeed: undefined,
          completedAt: new Date()
        });
      } else {
        // Show faster but still realistic download speeds
        const speeds = [
          '8.4 MB/s', '10.8 MB/s', '12.3 MB/s', '9.7 MB/s', '14.6 MB/s',
          '13.1 MB/s', '11.7 MB/s', '15.9 MB/s', '8.6 MB/s', '17.5 MB/s',
          '12.4 MB/s', '18.2 MB/s', '16.8 MB/s', '19.9 MB/s', '24.7 MB/s'
        ];
        const randomSpeed = speeds[Math.floor(Math.random() * speeds.length)];
        
        updateDownload(downloadId, { 
          progress: Math.round(progress),
          downloadSpeed: randomSpeed
        });
      }
    }, 600); // Faster updates (600ms)
  }, 1500); // Start download phase after 1.5 seconds
};

// Sample downloads for quick testing with realistic file examples
export const sampleDownloads = [
  {
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    filename: 'Rick Astley - Never Gonna Give You Up [4K].mp4',
    quality: '2160p'
  },
  {
    url: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
    filename: 'PSY - GANGNAM STYLE [1080p].mp4',
    quality: '1080p'
  },
  {
    url: 'https://vimeo.com/123456789',
    filename: 'Creative Portfolio Reel [1080p].mp4',
    quality: '1080p'
  },
  {
    url: 'https://example.com/report.pdf',
    filename: 'Q4 Financial Report.pdf',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=abcd1234',
    filename: 'Epic Movie Trailer [4K HDR].mp4',
    quality: '2160p'
  },
  {
    url: 'https://archive.org/download/big_buck_bunny',
    filename: 'Big Buck Bunny [720p].mp4',
    quality: '720p'
  },
  {
    url: 'https://example.com/presentation.pptx',
    filename: 'Company Presentation Q3.pptx',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=xyz789',
    filename: 'Tech Conference Keynote [1440p].mp4',
    quality: '1440p'
  },
  {
    url: 'https://example.com/album.zip',
    filename: 'Music Collection 2024.zip',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=learning123',
    filename: 'Programming Tutorial Series [1080p].mp4',
    quality: '1080p'
  },
  {
    url: 'https://example.com/ebook.epub',
    filename: 'Digital Marketing Guide.epub',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=music456',
    filename: 'Lofi Hip Hop Mix [1080p].mp4',
    quality: '1080p'
  },
  {
    url: 'https://example.com/dataset.csv',
    filename: 'Sales Data 2024.csv',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=travel789',
    filename: 'Travel Vlog - Japan [4K].mp4',
    quality: '2160p'
  },
  {
    url: 'https://example.com/software.dmg',
    filename: 'FlowEdit Pro v3.2.dmg',
    quality: 'auto'
  }
];

export const addSampleDownload = (downloadPath: string) => {
  const { addDownload } = useDownloadStore.getState();
  const sample = sampleDownloads[Math.floor(Math.random() * sampleDownloads.length)];
  
  // Use the provided download path
  addDownload(sample.url, sample.filename, sample.quality, downloadPath);
  
  // Find the newly added download and simulate its progress
  const { downloads } = useDownloadStore.getState();
  const newDownload = downloads[0]; // Most recent download
  
  // Start simulation after a short delay
  setTimeout(() => {
    simulateDownload(newDownload.id);
  }, 300);
};

// Add multiple sample downloads for testing the UI
export const addMultipleSampleDownloads = (downloadPath: string, count: number = 10) => {
  const { addDownload } = useDownloadStore.getState();
  
  // Take the first 'count' items from sample downloads to ensure variety
  const selectedSamples = sampleDownloads.slice(0, Math.min(count, sampleDownloads.length));
  
  selectedSamples.forEach((sample, index) => {
    setTimeout(() => {
      addDownload(sample.url, sample.filename, sample.quality, downloadPath);
      
      // Find the newly added download and simulate its progress
      const { downloads } = useDownloadStore.getState();
      const newDownload = downloads[0]; // Most recent download
      
      // Simulate different completion states for variety
      if (index < 3) {
        // First 3 downloads complete immediately
        setTimeout(() => {
          const { updateDownload } = useDownloadStore.getState();
          updateDownload(newDownload.id, {
            status: 'completed',
            progress: 100,
            completedAt: new Date(),
            fileSize: ['45.6 MB', '1.2 GB', '856 MB', '2.3 GB'][Math.floor(Math.random() * 4)]
          });
        }, 100);
      } else if (index < 6) {
        // Next 3 downloads simulate progress
        setTimeout(() => {
          simulateDownload(newDownload.id);
        }, 500 + index * 200);
      } else if (index < 8) {
        // 2 downloads stay pending
        // Do nothing - they'll remain in pending state
      } else {
        // Last 2 downloads have errors
        setTimeout(() => {
          const { updateDownload } = useDownloadStore.getState();
          updateDownload(newDownload.id, {
            status: 'error',
            progress: 0,
            error: 'Network timeout - please try again later'
          });
        }, 100);
      }
    }, index * 100); // Stagger the additions
  });
}; 