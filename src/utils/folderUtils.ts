import { toast } from 'react-toastify';

// Folder utility functions for download path management

/**
 * Check if we're running in a Tauri environment by attempting to import Tauri APIs
 */
const isInTauriEnvironment = async (): Promise<boolean> => {
  try {
    await import('@tauri-apps/api/core');
    return true;
  } catch {
    return false;
  }
};

/**
 * Resolves a path to ensure it's properly formatted for the filesystem
 */
export const resolvePath = async (path: string): Promise<string> => {
  if (await isInTauriEnvironment()) {
    try {
      // First try to resolve it using Tauri path API
      try {
        const { resolve } = await import('@tauri-apps/api/path');
        return await resolve(path);
      } catch (resolveError) {
        console.warn('Could not resolve path with Tauri API:', resolveError);
      }
      
      // If that fails, try the custom Rust commands for standard paths
      const { invoke } = await import('@tauri-apps/api/core');
      
      // If path starts with ~/
      if (path.startsWith('~/') || path === '~') {
        try {
          const homeDir = await invoke<string>('get_home_dir');
          if (path === '~') return homeDir;
          return `${homeDir}${path.substring(1)}`;
        } catch (e) {
          console.warn('Error resolving home directory:', e);
        }
      }
      
      // If path is just "Downloads"
      if (path === 'Downloads' || path === './Downloads' || path === '/Downloads') {
        try {
          return await invoke<string>('get_download_dir');
        } catch (e) {
          console.warn('Error resolving downloads directory:', e);
        }
      }
    } catch (error) {
      console.error('Error resolving path:', error);
    }
    
    // Return the original path if we can't resolve it
    return path;
  }
  // In web mode, just return the original path
  return path;
};

/**
 * Use Tauri API to check directory access directly
 */
export const checkDirectoryAccess = async (path: string): Promise<boolean> => {
  try {
    if (await isInTauriEnvironment()) {
      // First, try to resolve the path if it's relative or contains ~
      const resolvedPath = await resolvePath(path);
      console.log(`Checking directory access: "${path}" resolved to "${resolvedPath}"`);
      
      const { invoke } = await import('@tauri-apps/api/core');
      const result = await invoke('check_directory_access', { path: resolvedPath });
      return !!result;
    }
    // In web environment, assume it works
    return true;
  } catch (error) {
    console.error('Error checking directory access:', error);
    
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      if (error.stack) console.error('Stack:', error.stack);
    }
    
    return false;
  }
};

/**
 * Create a directory using Tauri commands
 */
export const createDirectory = async (path: string): Promise<boolean> => {
  try {
    if (await isInTauriEnvironment()) {
      // First, try to resolve the path if it's relative or contains ~
      const resolvedPath = await resolvePath(path);
      console.log(`Creating directory: "${path}" resolved to "${resolvedPath}"`);
      
      const { invoke } = await import('@tauri-apps/api/core');
      const result = await invoke('create_directory', { path: resolvedPath });
      return !!result;
    }
    // In web environment, assume it works
    return true;
  } catch (error) {
    console.error('Error creating directory:', error);
    
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      if (error.stack) console.error('Stack:', error.stack);
      
      // Provide more specific error messages
      if (error.message.includes('permission') || error.message.includes('denied')) {
        toast.error('Permission denied creating directory. Please check app permissions.');
      } else {
        toast.error(`Failed to create directory: ${error.message}`);
      }
    }
    
    return false;
  }
};

/**
 * Opens a folder picker dialog and returns the selected path.
 * Returns null if the user cancels or an error occurs.
 */
export const selectDownloadFolder = async (): Promise<string | null> => {
  try {
    if (await isInTauriEnvironment()) {
      // Show a loading message
      const loadingToast = toast.info('Opening folder picker...', { autoClose: false });
      
      try {
        // Make sure the dialog plugin is available
        const { open } = await import('@tauri-apps/plugin-dialog');
        
        // Clear the loading message
        toast.dismiss(loadingToast);
        
        // Open the folder picker with a clear title
        const selected = await open({
          directory: true,
          multiple: false,
          title: 'Select Download Folder',
        });
        
        if (selected === null) {
          console.log('Folder selection cancelled by user');
          return null; // User cancelled
        }
        
        console.log('Selected folder path:', selected);
        
        // Check if we have access to this directory
        const hasAccess = await checkDirectoryAccess(selected as string);
        if (!hasAccess) {
          // Try to create the directory to ensure we have access
          const created = await createDirectory(selected as string);
          if (!created) {
            toast.error('Cannot access the selected folder. Please check permissions and try again.');
            return null;
          }
        }
        
        // Folder is accessible - show success
        toast.success('Folder selected successfully');
        
        // Return the absolute path
        return selected as string;
      } catch (error) {
        // Clear the loading message
        toast.dismiss(loadingToast);
        
        // Log detailed error
        console.error('Error opening folder picker:', error);
        
        // Check for specific error types
        if (error instanceof Error) {
          const errorMsg = error.message.toLowerCase();
          
          if (errorMsg.includes('permission') || errorMsg.includes('denied')) {
            toast.error('Permission denied. Please grant folder access permissions.');
          } else if (errorMsg.includes('not available') || errorMsg.includes('unsupported')) {
            toast.error('Folder picker is not available on this platform.');
          } else if (errorMsg.includes('plugin')) {
            toast.error('Dialog plugin error. Try restarting the application.');
          } else if (errorMsg.includes('timeout')) {
            toast.error('Folder picker timed out. Please try again.');
          } else {
            toast.error(`Could not open folder picker: ${error.message}`);
          }
        } else {
          toast.error('Could not open folder picker. Please try again or check permissions.');
        }
        
        return null;
      }
    } else {
      toast.warn('Folder picker is not available in this environment (web/dev mode).');
      console.warn('Folder picker not available in web environment. A default or previously set path will be used.');
      return null; // Or return a mock path for testing if needed
    }
  } catch (error) {
    console.error('Error in selectDownloadFolder function:', error);
    
    // Provide a fallback error message for unexpected issues
    toast.error('Could not open folder picker due to an unexpected error. Please restart the application.');
    return null;
  }
};

/**
 * Gets the user's default Downloads folder path.
 * In a non-Tauri environment, provides a sensible fallback.
 */
export const getDefaultDownloadsPath = async (): Promise<string> => {
  try {
    if (await isInTauriEnvironment()) {
      // Try using our direct Rust function first for better reliability
      try {
        const { invoke } = await import('@tauri-apps/api/core');
        const downloadDir = await invoke<string>('get_download_dir');
        
        if (downloadDir) {
          console.log('Got download directory from Rust command:', downloadDir);
          
          // Verify we can access it
          const hasAccess = await checkDirectoryAccess(downloadDir);
          if (hasAccess) {
            return downloadDir;
          } else {
            console.warn('Cannot access download directory from Rust command');
            
            // Try to create the directory
            const created = await createDirectory(downloadDir);
            if (created) {
              return downloadDir;
            }
          }
        }
      } catch (rustError) {
        console.warn('Error getting download directory from Rust command:', rustError);

        // If it's a specific error about directory not existing, try to create it
        if (rustError instanceof Error && rustError.message.includes('does not exist')) {
          console.log('Downloads directory does not exist, attempting to create it...');
          try {
            const { homeDir } = await import('@tauri-apps/api/path');
            const home = await homeDir();
            if (home) {
              const { join } = await import('@tauri-apps/api/path');
              const downloadsPath = await join(home, 'Downloads');
              const created = await createDirectory(downloadsPath);
              if (created) {
                console.log('Successfully created Downloads directory:', downloadsPath);
                return downloadsPath;
              }
            }
          } catch (createError) {
            console.warn('Failed to create Downloads directory:', createError);
          }
        }
      }
      
      // Try to get local app data directory as fallback
      try {
        const { invoke } = await import('@tauri-apps/api/core');
        const localDataDir = await invoke<string>('get_app_local_data_dir');
        
        if (localDataDir) {
          // Use proper path joining instead of string concatenation
          const { join } = await import('@tauri-apps/api/path');
          const downloadsPath = await join(localDataDir, 'Downloads');
          console.log('Using app data directory for downloads:', downloadsPath);

          // Make sure it exists
          const created = await createDirectory(downloadsPath);
          if (created) {
            return downloadsPath;
          }
        }
      } catch (localDirError) {
        console.warn('Error getting app local data directory:', localDirError);
      }
      
      // Try using the Tauri path API as fallback
      try {
        const { downloadDir } = await import('@tauri-apps/api/path');
        const dir = await downloadDir();
        
        if (!dir) {
          console.warn('downloadDir() returned null/empty, trying alternative approach');
          
          // Try alternative approaches
          try {
            const { homeDir } = await import('@tauri-apps/api/path');
            const home = await homeDir();
            if (home) {
              // Use proper path joining instead of string concatenation
              const { join } = await import('@tauri-apps/api/path');
              const defaultPath = await join(home, 'Downloads');
              console.log('Using constructed Downloads path:', defaultPath);
              
              // Check if we can access this directory
              const hasAccess = await checkDirectoryAccess(defaultPath);
              if (!hasAccess) {
                // Try to create it
                const created = await createDirectory(defaultPath);
                if (!created) {
                  console.warn('Could not access or create Downloads folder in home directory');
                  return await resolvePath('Downloads');
                }
              }
              
              return defaultPath;
            }
          } catch (homeError) {
            console.error('Could not get home directory:', homeError);
          }
          
          // Final fallback within Tauri
          toast.warn('Could not determine default downloads directory. Using "Downloads" folder.');
          return await resolvePath('Downloads');
        }
        
        console.log('Using default downloads path:', dir);
        
        // Check if we can access this directory
        const hasAccess = await checkDirectoryAccess(dir);
        if (!hasAccess) {
          console.warn('Cannot access default downloads directory, using fallback');
          return await resolvePath('Downloads');
        }
        
        return dir;
      } catch (pathError) {
        console.error('Error getting downloads directory via Tauri:', pathError);
        
        if (pathError instanceof Error && pathError.message.includes('permission')) {
          toast.error('Permission denied accessing Downloads folder. Please check app permissions.');
        } else {
          toast.warn('Could not access Downloads folder. Using fallback location.');
        }
        
        // Try to use the current working directory + Downloads
        try {
          // Tauri v2 doesn't have currentDir, so use homeDir instead
          const { homeDir } = await import('@tauri-apps/api/path');
          const home = await homeDir();
          // Use proper path joining instead of string concatenation
          const { join } = await import('@tauri-apps/api/path');
          const downloadPath = await join(home, 'Downloads');
          
          // Try to create this directory
          const created = await createDirectory(downloadPath);
          if (created) {
            return downloadPath;
          }
        } catch {
          // If that fails too, use a simple path
        }
        
        return await resolvePath('Downloads');
      }
    } else {
      // Fallback paths for web/dev environment
      console.warn('Using fallback default downloads path for web/dev environment.');
      // Use userAgent instead of deprecated platform
      const userAgent = window.navigator.userAgent.toLowerCase();
      if (userAgent.includes('mac')) {
        return '~/Downloads';
      } else if (userAgent.includes('win')) {
        return 'Downloads';
      } else {
        return '~/Downloads';
      }
    }
  } catch (error) {
    console.error('Error getting default downloads directory:', error);
    
    if (error instanceof Error && error.message.includes('permission')) {
      toast.error('Permission denied. Please check app permissions and try again.');
    } else {
      toast.error('Could not get default downloads directory. Using fallback.');
    }
    
    return 'Downloads'; // Final fallback
  }
};

/**
 * Formats a file path for display (shortens long paths)
 */
export const formatPathForDisplay = (path: string, maxLength: number = 40): string => {
  if (!path) return 'Not set';
  if (path.length <= maxLength) {
    return path;
  }
  
  const separator = path.includes('/') ? '/' : '\\';
  const parts = path.split(separator);
  if (parts.length > 2) {
    // Show first part, ellipsis, and last part (filename typically)
    const firstPart = parts[0];
    const lastPart = parts[parts.length - 1];
    const formatted = `${firstPart}${separator}...${separator}${lastPart}`;
    return formatted.length <= maxLength ? formatted : `${path.substring(0, maxLength - 3)}...`;
  }
  
  // Default shortening if not many parts
  return `${path.substring(0, maxLength - 3)}...`;
};

/**
 * Checks if a path exists and is accessible (Tauri specific).
 * For enterprise-ready, actual write/read checks might be needed if permissions are granular.
 */
export const validateDownloadPath = async (path: string): Promise<boolean> => {
  if (!path) return false;
  try {
    if (await isInTauriEnvironment()) {
      try {
        // First try to resolve the path if it's relative or has special characters
        const resolvedPath = await resolvePath(path);
        console.log('Validating path:', path, 'Resolved to:', resolvedPath);
        
        // Use our direct directory access check
        const hasAccess = await checkDirectoryAccess(resolvedPath);
        if (hasAccess) {
          return true;
        }
        
        // Try to create the directory if it doesn't exist
        console.log('Path not accessible, attempting to create it:', resolvedPath);
        const created = await createDirectory(resolvedPath);
        if (created) {
          toast.success(`Created download directory: ${formatPathForDisplay(resolvedPath, 60)}`);
          return true;
        } else {
          toast.error(`Could not create or access download directory: ${formatPathForDisplay(resolvedPath, 60)}`);
          return false;
        }
      } catch (fsError) {
        console.error('File system error during validation:', fsError);
        
        if (fsError instanceof Error && fsError.message.includes('permission')) {
          toast.error('Permission denied accessing the folder. Please check app permissions.');
        } else {
          toast.error('Could not access the selected folder. Please choose another location.');
        }
        
        return false;
      }
    } else {
      // In web environment, assume path is valid for demo purposes.
      return true;
    }
  } catch (error) {
    console.error('Error validating path:', error);
    
    if (error instanceof Error && error.message.includes('permission')) {
      toast.error('Permission denied. Please check app permissions and try again.');
    } else {
      toast.error('Could not validate selected path. Please check permissions or choose another.');
    }
    
    return false;
  }
}; 