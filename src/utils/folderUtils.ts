// Enhanced folder utilities with comprehensive error handling
import { invoke } from '@tauri-apps/api/core';

// Check if we're in a Tauri environment
export const isInTauri = (): boolean => {
  return typeof window !== 'undefined' && window.__TAURI__ !== undefined;
};

/**
 * Get the default downloads directory with comprehensive fallback strategy
 */
export const getDefaultDownloadsPath = async (): Promise<string> => {
  if (!isInTauri()) {
    // Web/development fallback
    const userAgent = window.navigator.userAgent.toLowerCase();
    if (userAgent.includes('mac')) {
      return '~/Downloads';
    } else if (userAgent.includes('win')) {
      return 'Downloads';
    } else {
      return '~/Downloads';
    }
  }

  try {
    // Use the new comprehensive downloads folder command
    const downloadsPath = await invoke<string>('get_downloads_folder');
    console.log('✅ Downloads folder resolved:', downloadsPath);
    return downloadsPath;
  } catch (error) {
    console.error('❌ Failed to get downloads folder:', error);
    
    // Try the old method as fallback
    try {
      const fallbackPath = await invoke<string>('get_download_dir');
      console.log('✅ Using fallback downloads path:', fallbackPath);
      return fallbackPath;
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError);
      
      // Last resort: use a reasonable default
      const defaultPath = await getLastResortPath();
      console.log('⚠️ Using last resort path:', defaultPath);
      return defaultPath;
    }
  }
};

/**
 * Ensure a directory exists and is writable
 */
export const ensureDirectoryExists = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    console.log('Web environment: assuming directory exists');
    return true;
  }

  try {
    const result = await invoke<boolean>('ensure_directory_exists', { path });
    if (result) {
      console.log('✅ Directory ensured:', path);
    } else {
      console.warn('⚠️ Directory not accessible:', path);
    }
    return result;
  } catch (error) {
    console.error('❌ Failed to ensure directory exists:', error);
    return false;
  }
};

/**
 * Test if a directory is accessible and writable
 */
export const testDirectoryAccess = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    return true;
  }

  try {
    const result = await invoke<boolean>('test_directory_access', { path });
    console.log(result ? '✅ Directory accessible:' : '❌ Directory not accessible:', path);
    return result;
  } catch (error) {
    console.error('❌ Error testing directory access:', error);
    return false;
  }
};

/**
 * Open a folder in the system file explorer
 */
export const openFolderInExplorer = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    console.warn('Cannot open folder in web environment');
    return false;
  }

  try {
    await invoke('open_folder_in_explorer', { path });
    console.log('✅ Opened folder in explorer:', path);
    return true;
  } catch (error) {
    console.error('❌ Failed to open folder:', error);
    return false;
  }
};

/**
 * Resolve a path (handle ~ expansion, relative paths, etc.)
 */
export const resolvePath = async (path: string): Promise<string> => {
  if (!isInTauri()) {
    // Simple web fallback
    return path.replace(/^~\//, '/Users/');
  }

  try {
    const resolvedPath = await invoke<string>('resolve_path', { path });
    console.log('✅ Path resolved:', path, '->', resolvedPath);
    return resolvedPath;
  } catch (error) {
    console.error('❌ Failed to resolve path:', error);
    return path;
  }
};

/**
 * Get a reasonable last resort path when all else fails
 */
const getLastResortPath = async (): Promise<string> => {
  try {
    // Try to get home directory and create Downloads there
    const home = await invoke<string>('get_home_dir');
    const downloadsPath = `${home}/Downloads`;
    
    // Try to ensure it exists
    await ensureDirectoryExists(downloadsPath);
    return downloadsPath;
  } catch (error) {
    console.error('❌ Even last resort failed:', error);
    
    // Ultimate fallback - use current directory
    return './downloads';
  }
};

/**
 * Format a path for display (shorten long paths)
 */
export const formatPathForDisplay = (path: string, maxLength: number = 50): string => {
  if (path.length <= maxLength) {
    return path;
  }
  
  // Show start and end of path
  const start = path.substring(0, 15);
  const end = path.substring(path.length - 30);
  return `${start}...${end}`;
};

/**
 * Validate that a path is safe and reasonable for downloads
 */
export const validateDownloadPath = async (path: string): Promise<boolean> => {
  if (!path || path.trim().length === 0) {
    return false;
  }

  // Check for obviously bad paths
  const badPaths = ['/System', '/Windows', '/usr', '/bin', '/sbin'];
  const normalizedPath = path.toLowerCase();
  
  for (const badPath of badPaths) {
    if (normalizedPath.startsWith(badPath.toLowerCase())) {
      console.warn('⚠️ Rejecting system path:', path);
      return false;
    }
  }

  // Test if the path is accessible
  try {
    const resolvedPath = await resolvePath(path);
    return await testDirectoryAccess(resolvedPath);
  } catch (error) {
    console.error('❌ Path validation failed:', error);
    return false;
  }
};

/**
 * Get suggested download paths for the user to choose from
 */
export const getSuggestedDownloadPaths = async (): Promise<string[]> => {
  const suggestions: string[] = [];
  
  if (!isInTauri()) {
    return ['Downloads', '~/Downloads'];
  }

  try {
    // Primary suggestion: system downloads folder
    try {
      const downloadsPath = await invoke<string>('get_downloads_folder');
      suggestions.push(downloadsPath);
    } catch (e) {
      console.log('Primary downloads folder not available');
    }

    // Secondary suggestions
    try {
      const home = await invoke<string>('get_home_dir');
      suggestions.push(`${home}/Downloads`);
      suggestions.push(`${home}/Desktop/FlowDownload`);
      suggestions.push(`${home}/Documents/FlowDownload`);
    } catch (e) {
      console.log('Home directory not available');
    }

    // Remove duplicates and test accessibility
    const uniqueSuggestions = [...new Set(suggestions)];
    const accessibleSuggestions: string[] = [];

    for (const suggestion of uniqueSuggestions) {
      try {
        if (await testDirectoryAccess(suggestion)) {
          accessibleSuggestions.push(suggestion);
        }
      } catch (e) {
        console.log('Suggestion not accessible:', suggestion);
      }
    }

    return accessibleSuggestions.length > 0 ? accessibleSuggestions : ['./downloads'];
  } catch (error) {
    console.error('❌ Failed to get suggested paths:', error);
    return ['./downloads'];
  }
};

/**
 * Select a download folder using the system file dialog
 */
export const selectDownloadFolder = async (): Promise<string | null> => {
  if (!isInTauri()) {
    console.warn('Folder selection not available in web environment');
    return null;
  }

  try {
    const { open } = await import('@tauri-apps/plugin-dialog');
    const result = await open({
      directory: true,
      multiple: false,
      title: 'Select Download Folder'
    });

    if (result && typeof result === 'string') {
      // Test if the selected folder is accessible
      const isAccessible = await testDirectoryAccess(result);
      if (isAccessible) {
        console.log('✅ Selected accessible folder:', result);
        return result;
      } else {
        console.warn('⚠️ Selected folder is not accessible:', result);
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('❌ Failed to select folder:', error);
    return null;
  }
};

// Legacy function names for backward compatibility
export const createDirectory = ensureDirectoryExists;
export const checkDirectoryAccess = testDirectoryAccess;