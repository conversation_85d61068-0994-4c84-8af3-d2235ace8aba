import { useDownloadStore } from '../store/downloadStore';

// Enhanced demo function to simulate realistic download phases
export const simulateDownload = (downloadId: string) => {
  const { updateDownload } = useDownloadStore.getState();
  
  // Phase 1: Extract video information (1-2 seconds)
  updateDownload(downloadId, { 
    status: 'downloading', 
    progress: 0,
    downloadSpeed: undefined,
    fileSize: 'Calculating...'
  });

  // Simulate extracting video info
  setTimeout(() => {
    updateDownload(downloadId, { 
      progress: 5,
      fileSize: 'Extracting video information...'
    });
  }, 500);

  // Phase 2: Selecting best format
  setTimeout(() => {
    updateDownload(downloadId, { 
      progress: 15,
      fileSize: 'Selecting optimal format...'
    });
  }, 1000);

  // Phase 3: Start actual download with file size
  setTimeout(() => {
    const fileSizes = ['856 MB', '1.2 GB', '2.3 GB', '1.8 GB', '3.1 GB'];
    const randomSize = fileSizes[Math.floor(Math.random() * fileSizes.length)];
    
    updateDownload(downloadId, { 
      progress: 25,
      downloadSpeed: '8.4 MB/s',
      fileSize: randomSize
    });

    // Continue with faster download simulation
    let progress = 25;
    const downloadInterval = setInterval(() => {
      progress += Math.random() * 8 + 5; // Faster progress increment (5-13% per update)
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(downloadInterval);
        updateDownload(downloadId, { 
          status: 'completed', 
          progress: 100,
          downloadSpeed: undefined,
          completedAt: new Date()
        });
      } else {
        // Show faster but still realistic download speeds
        const speeds = [
          '8.4 MB/s', '10.8 MB/s', '12.3 MB/s', '9.7 MB/s', '14.6 MB/s',
          '13.1 MB/s', '11.7 MB/s', '15.9 MB/s', '8.6 MB/s', '17.5 MB/s',
          '12.4 MB/s', '18.2 MB/s', '16.8 MB/s', '19.9 MB/s', '24.7 MB/s'
        ];
        const randomSpeed = speeds[Math.floor(Math.random() * speeds.length)];
        
        updateDownload(downloadId, { 
          progress: Math.round(progress),
          downloadSpeed: randomSpeed
        });
      }
    }, 600); // Faster updates (600ms)
  }, 1500); // Start download phase after 1.5 seconds
};

// Demo downloads for testing with larger file sizes
export const demoDownloads = [
  {
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    filename: 'Rick Astley - Never Gonna Give You Up [4K].mp4',
    quality: '2160p'
  },
  {
    url: 'https://vimeo.com/123456789',
    filename: 'Sample Video [1080p].mp4',
    quality: '1080p'
  },
  {
    url: 'https://example.com/sample.pdf',
    filename: 'Large Document.pdf',
    quality: 'auto'
  },
  {
    url: 'https://www.youtube.com/watch?v=abcd1234',
    filename: 'Epic Movie Trailer [4K HDR].mp4',
    quality: '2160p'
  }
];

export const addDemoDownload = (downloadPath: string) => {
  const { addDownload } = useDownloadStore.getState();
  const demo = demoDownloads[Math.floor(Math.random() * demoDownloads.length)];
  
  // Use the provided download path
  addDownload(demo.url, demo.filename, demo.quality, downloadPath);
  
  // Find the newly added download and simulate its progress
  const { downloads } = useDownloadStore.getState();
  const newDownload = downloads[0]; // Most recent download
  
  // Start simulation after a short delay
  setTimeout(() => {
    simulateDownload(newDownload.id);
  }, 300);
}; 