import { create } from 'zustand';
import { getDefaultDownloadsPath } from '../utils/folderUtils';
import { getConfig } from '../config/configManager';
import { ERROR_MESSAGES } from '../config/constants';

export interface Download {
  id: string;
  url: string;
  filename: string;
  quality: string; // Video quality: 'auto', '144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'
  downloadPath: string; // Where the file will be saved
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'paused';
  error?: string;
  createdAt: Date;
  completedAt?: Date;
  downloadSpeed?: string; // e.g., "1.2 MB/s"
  fileSize?: string; // e.g., "45.6 MB"
  estimatedTimeRemaining?: string; // e.g., "2m 30s"
  bytesDownloaded?: number; // Raw bytes downloaded
  bytesTotal?: number; // Raw total bytes
}

interface DownloadState {
  downloads: Download[];
  defaultDownloadPath: string;
  isInitialized: boolean;
  addDownload: (url: string, filename: string, quality: string, downloadPath: string) => void;
  updateDownload: (id: string, updates: Partial<Download>) => void;
  removeDownload: (id: string) => void;
  clearCompleted: () => void;
  setDefaultDownloadPath: (path: string) => void;
  initializeDefaultPath: () => Promise<void>;
  startRealDownload: (download: Download) => Promise<void>;
}

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Helper function to detect social media URLs
const isSocialMediaUrl = (url: string): boolean => {
  const config = getConfig();
  const socialMediaDomains = config.security.allowedDomains;
  
  try {
    // Try to parse the URL to handle both URL objects and strings
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // Check if any social media domain is in the hostname
    return socialMediaDomains.some(domain => hostname.includes(domain));
  } catch (e) {
    // If URL parsing fails, do a simple string match
    return socialMediaDomains.some(domain => url.toLowerCase().includes(domain));
  }
};

export const useDownloadStore = create<DownloadState>((set, get) => ({
  downloads: [],
  defaultDownloadPath: 'Downloads', // Temporary fallback until initialized
  isInitialized: false,
  
  initializeDefaultPath: async () => {
    try {
      console.log('Initializing default download path...');
      const defaultPath = await getDefaultDownloadsPath();
      console.log('Got default path:', defaultPath);
      
      set({ 
        defaultDownloadPath: defaultPath,
        isInitialized: true 
      });
    } catch (error) {
      console.error('Failed to initialize default download path:', error);
      set({ 
        defaultDownloadPath: 'Downloads',
        isInitialized: true 
      });
    }
  },
  
  addDownload: (url: string, filename: string, quality: string, downloadPath: string) => {
    const newDownload: Download = {
      id: `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      url,
      filename,
      quality,
      downloadPath,
      status: 'pending',
      progress: 0,
      createdAt: new Date(),
    };
    
    set((state) => ({
      downloads: [newDownload, ...state.downloads],
    }));

    // Start real download after state update using Promise.resolve for proper async handling
    Promise.resolve().then(() => {
      get().startRealDownload(newDownload);
    });
  },
  
  updateDownload: (id: string, updates: Partial<Download>) => {
    set((state) => ({
      downloads: state.downloads.map((download) =>
        download.id === id ? { ...download, ...updates } : download
      ),
    }));
  },
  
  removeDownload: (id: string) => {
    // Clean up any pending listeners before removing
    if (window.downloadCleanupMap?.has(id)) {
      const cleanup = window.downloadCleanupMap.get(id);
      try {
        cleanup?.();
      } catch (error) {
        console.warn('Error during cleanup:', error);
      }
      window.downloadCleanupMap.delete(id);
    }

    set((state) => ({
      downloads: state.downloads.filter((download) => download.id !== id),
    }));
  },
  
  clearCompleted: () => {
    set((state) => {
      // Clean up listeners for completed downloads before removing them
      const completedDownloads = state.downloads.filter((download) => download.status === 'completed');
      completedDownloads.forEach((download) => {
        if (window.downloadCleanupMap?.has(download.id)) {
          const cleanup = window.downloadCleanupMap.get(download.id);
          try {
            cleanup?.();
          } catch (error) {
            console.warn('Error during cleanup:', error);
          }
          window.downloadCleanupMap.delete(download.id);
        }
      });

      return {
        downloads: state.downloads.filter((download) => download.status !== 'completed'),
      };
    });
  },

  setDefaultDownloadPath: (path: string) => {
    set({ defaultDownloadPath: path });
  },

  startRealDownload: async (download: Download) => {
    const { updateDownload } = get();
    
    try {
      // Check if we're in a Tauri environment
      const isInTauri = async (): Promise<boolean> => {
        try {
          await import('@tauri-apps/api/core');
          return true;
        } catch {
          return false;
        }
      };

      if (await isInTauri()) {
        let invoke: typeof import('@tauri-apps/api/core').invoke;
        let listen: typeof import('@tauri-apps/api/event').listen;
        
        try {
          const coreModule = await import('@tauri-apps/api/core');
          const eventModule = await import('@tauri-apps/api/event');
          invoke = coreModule.invoke;
          listen = eventModule.listen;
        } catch (importError) {
          console.error('Failed to import Tauri APIs:', importError);
          throw new Error('Tauri API not available');
        }
        
        // Set up progress listener with enhanced metrics and error handling
        let progressUnlisten: (() => void) | undefined;
        try {
          progressUnlisten = await listen('download-progress', (event: { payload?: unknown }) => {
            try {
              const payload = event?.payload || event;
              if (payload && typeof payload === 'object' && 'id' in payload && (payload as Record<string, unknown>).id === download.id) {
                const typedPayload = payload as Record<string, unknown>;
                const total = Number(typedPayload.total) || 0;
                const downloaded = Number(typedPayload.downloaded) || 0;
                const progress = total > 0 ? Math.round((downloaded / total) * 100) : 0;

                updateDownload(download.id, {
                  status: 'downloading',
                  progress,
                  fileSize: formatFileSize(total),
                  downloadSpeed: String(typedPayload.speed || 'Calculating...'),
                  estimatedTimeRemaining: String(typedPayload.estimated_remaining || ''),
                  bytesDownloaded: downloaded,
                  bytesTotal: total
                });
              }
            } catch (error) {
              console.error('Error in progress listener:', error);
            }
          });
        } catch (listenerError) {
          console.error('Failed to set up progress listener:', listenerError);
        }

        // Set up completion listener with error handling
        let completeUnlisten: (() => void) | undefined;
        try {
          completeUnlisten = await listen('download-complete', (event: { payload?: unknown }) => {
            try {
              const payload = event?.payload || event;
              if (payload === download.id || (typeof payload === 'object' && payload && 'id' in payload && (payload as Record<string, unknown>).id === download.id)) {
                updateDownload(download.id, {
                  status: 'completed',
                  progress: 100,
                  downloadSpeed: undefined,
                  estimatedTimeRemaining: undefined,
                  completedAt: new Date(),
                });
                // Clean up listeners
                try {
                  progressUnlisten?.();
                  completeUnlisten?.();
                } catch (cleanupError) {
                  console.warn('Error cleaning up listeners:', cleanupError);
                }
              }
            } catch (error) {
              console.error('Error in completion listener:', error);
            }
          });
        } catch (listenerError) {
          console.error('Failed to set up completion listener:', listenerError);
        }

        // Set up error listener with proper cleanup and error handling
        let errorUnlisten: (() => void) | undefined;
        try {
          errorUnlisten = await listen('download-error', (event: { payload?: unknown }) => {
            try {
              const payload = event?.payload || event;
              const errorData = typeof payload === 'string' ? { id: payload } : payload;
              if (errorData && errorData.id === download.id) {
                updateDownload(download.id, {
                  status: 'error',
                  progress: 0,
                  error: errorData.error || ERROR_MESSAGES.DOWNLOAD_FAILED,
                });
                // Clean up all listeners
                try {
                  progressUnlisten?.();
                  completeUnlisten?.();
                  errorUnlisten?.();
                } catch (cleanupError) {
                  console.warn('Error cleaning up error listeners:', cleanupError);
                }
              }
            } catch (error) {
              console.error('Error in error listener:', error);
            }
          });
        } catch (listenerError) {
          console.error('Failed to set up error listener:', listenerError);
        }

        // Store cleanup function for potential cancellation
        const cleanup = () => {
          progressUnlisten?.();
          completeUnlisten?.();
          errorUnlisten?.();
        };

        // Add cleanup to a Map for later use if needed
        if (!window.downloadCleanupMap) {
          window.downloadCleanupMap = new Map();
        }
        window.downloadCleanupMap.set(download.id, cleanup);

        // Start the download
        updateDownload(download.id, { status: 'downloading' });
        
        const filePath = `${download.downloadPath}/${download.filename}`;
        
        // Special handling for social media platforms
        if (isSocialMediaUrl(download.url)) {
          console.log('Social media URL detected, checking platform...');
          
          let errorMessage = '';
          if (download.url.includes('youtube.com') || download.url.includes('youtu.be')) {
            errorMessage = 'YouTube videos require yt-dlp which is not installed. Please install yt-dlp to download YouTube videos.';
          } else if (download.url.includes('twitter.com') || download.url.includes('x.com')) {
            errorMessage = 'Twitter/X downloads require yt-dlp or gallery-dl which is not installed. Please install one of these tools to download Twitter videos.';
          } else if (download.url.includes('facebook.com') || download.url.includes('fb.com') || download.url.includes('fb.watch')) {
            errorMessage = 'Facebook downloads require yt-dlp or gallery-dl which is not installed. Please install one of these tools to download Facebook videos.';
          } else if (download.url.includes('instagram.com')) {
            errorMessage = 'Instagram downloads require gallery-dl or instaloader which is not installed. Please install one of these tools to download Instagram content.';
          } else {
            errorMessage = 'This platform requires specialized download tools which are not installed.';
          }
          
          updateDownload(download.id, {
            status: 'error',
            error: errorMessage
          });
          return;
        }
        
        try {
          await invoke('download_file', {
            id: download.id,
            url: download.url,
            file_path: filePath, // Note: using snake_case for Rust compatibility
          });
        } catch (error: unknown) {
          console.error('Download failed:', error);
          updateDownload(download.id, {
            status: 'error',
            error: error instanceof Error ? error.message : 'Download failed',
          });
        }
      } else {
        // Fallback to simulation for web environment
        console.log('Using simulation in web environment');
        const { simulateDownload } = await import('../utils/demoDownloads');
        setTimeout(() => simulateDownload(download.id), 1000);
      }
    } catch (error: unknown) {
      console.error('Download failed:', error);
      updateDownload(download.id, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Download failed',
      });
    }
  },
})); 