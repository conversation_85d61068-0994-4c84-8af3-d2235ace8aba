import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { Plugin, PluginSearchResult } from '../plugins/types';
import { pluginRegistry } from '../plugins';

interface PluginManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const PluginManager: React.FC<PluginManagerProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'installed' | 'browse' | 'settings'>('installed');
  const [installedPlugins, setInstalledPlugins] = useState<Plugin[]>([]);
  const [searchResults, setSearchResults] = useState<PluginSearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadInstalledPlugins();
    }
  }, [isOpen]);

  const loadInstalledPlugins = () => {
    const plugins = Array.from(pluginRegistry.plugins.values());
    setInstalledPlugins(plugins);
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsLoading(true);
    try {
      const results = await pluginRegistry.searchPlugins(searchQuery);
      setSearchResults(results);
    } catch (error) {
      console.error('Plugin search failed:', error);
      toast.error('Failed to search plugins');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePlugin = async (pluginName: string, enabled: boolean) => {
    try {
      if (enabled) {
        await pluginRegistry.enable(pluginName);
        toast.success(`Plugin ${pluginName} enabled`);
      } else {
        await pluginRegistry.disable(pluginName);
        toast.success(`Plugin ${pluginName} disabled`);
      }
      loadInstalledPlugins();
    } catch (error) {
      console.error('Failed to toggle plugin:', error);
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} plugin`);
    }
  };

  const handleUninstallPlugin = async (pluginName: string) => {
    if (!confirm(`Are you sure you want to uninstall ${pluginName}?`)) {
      return;
    }

    try {
      await pluginRegistry.uninstall(pluginName);
      toast.success(`Plugin ${pluginName} uninstalled`);
      loadInstalledPlugins();
    } catch (error) {
      console.error('Failed to uninstall plugin:', error);
      toast.error('Failed to uninstall plugin');
    }
  };

  const handleInstallPlugin = async (pluginName: string) => {
    try {
      setIsLoading(true);
      // In a real implementation, this would download and install the plugin
      toast.info(`Installing ${pluginName}... (Demo mode)`);
      
      // Mock installation delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success(`Plugin ${pluginName} installed successfully`);
      loadInstalledPlugins();
    } catch (error) {
      console.error('Failed to install plugin:', error);
      toast.error('Failed to install plugin');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-4/5 h-4/5 max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Plugin Manager</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'installed', label: 'Installed', count: installedPlugins.length },
            { id: 'browse', label: 'Browse', count: null },
            { id: 'settings', label: 'Settings', count: null }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'installed' && (
            <div className="h-full overflow-y-auto p-6">
              {installedPlugins.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-6xl mb-4">🧩</div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No plugins installed</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Browse the plugin marketplace to extend FlowDownload&apos;s capabilities
                  </p>
                  <button
                    onClick={() => setActiveTab('browse')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Browse Plugins
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {installedPlugins.map(plugin => (
                    <div key={plugin.manifest.name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {plugin.manifest.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            v{plugin.manifest.version} by {plugin.manifest.author}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleTogglePlugin(plugin.manifest.name, !plugin.enabled)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              plugin.enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                plugin.enabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        {plugin.manifest.description}
                      </p>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {plugin.manifest.keywords.map(keyword => (
                          <span
                            key={keyword}
                            className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className={`text-sm ${plugin.enabled ? 'text-green-600' : 'text-gray-500'}`}>
                          {plugin.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                        <button
                          onClick={() => handleUninstallPlugin(plugin.manifest.name)}
                          className="text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Uninstall
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'browse' && (
            <div className="h-full overflow-y-auto p-6">
              {/* Search */}
              <div className="mb-6">
                <div className="flex space-x-4">
                  <input
                    type="text"
                    placeholder="Search plugins..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={handleSearch}
                    disabled={isLoading}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isLoading ? 'Searching...' : 'Search'}
                  </button>
                </div>
              </div>

              {/* Search Results */}
              {searchResults.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {searchResults.map(plugin => (
                    <div key={plugin.name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="mb-3">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {plugin.name}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          v{plugin.version} by {plugin.author}
                        </p>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        {plugin.description}
                      </p>
                      
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>⭐ {plugin.rating}</span>
                          <span>📥 {plugin.downloads.toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {plugin.tags.map(tag => (
                          <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      <button
                        onClick={() => handleInstallPlugin(plugin.name)}
                        disabled={isLoading || installedPlugins.some(p => p.manifest.name === plugin.name)}
                        className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        {installedPlugins.some(p => p.manifest.name === plugin.name) ? 'Installed' : 'Install'}
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-6xl mb-4">🔍</div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Discover Plugins</h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Search for plugins to extend FlowDownload&apos;s functionality
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="h-full overflow-y-auto p-6">
              <div className="max-w-2xl">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Plugin Settings</h3>
                
                <div className="space-y-6">
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                        Enable automatic plugin updates
                      </span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                        Allow plugins to send analytics data
                      </span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                        Enable developer mode (allows loading unsigned plugins)
                      </span>
                    </label>
                  </div>
                </div>
                
                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    Clear Plugin Cache
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PluginManager;