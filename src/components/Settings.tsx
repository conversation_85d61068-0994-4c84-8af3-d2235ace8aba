import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { configManager, getConfig, updateConfig } from '../config/configManager';
import { AppConfig, QUALITY_OPTIONS } from '../config/constants';

interface SettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const Settings: React.FC<SettingsProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState<AppConfig>(getConfig());
  const [activeTab, setActiveTab] = useState<'general' | 'downloads' | 'network' | 'advanced'>('general');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setConfig(getConfig());
    }
  }, [isOpen]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await updateConfig(config);
      toast.success('Settings saved successfully!');
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = async () => {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      setIsLoading(true);
      try {
        await configManager.resetConfig();
        setConfig(getConfig());
        toast.success('Settings reset to defaults');
      } catch (error) {
        console.error('Failed to reset settings:', error);
        toast.error('Failed to reset settings');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const updateConfigValue = (path: string, value: unknown) => {
    const keys = path.split('.');
    const newConfig = { ...config };
    let current: Record<string, unknown> = newConfig as Record<string, unknown>;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]] as Record<string, unknown>;
    }
    current[keys[keys.length - 1]] = value;
    
    setConfig(newConfig);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-4/5 h-4/5 max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'general', label: 'General' },
            { id: 'downloads', label: 'Downloads' },
            { id: 'network', label: 'Network' },
            { id: 'advanced', label: 'Advanced' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as typeof activeTab)}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Theme
                </label>
                <select
                  value={config.ui.theme}
                  onChange={(e) => updateConfigValue('ui.theme', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifications"
                  checked={config.ui.notifications}
                  onChange={(e) => updateConfigValue('ui.notifications', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="notifications" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="minimizeToTray"
                  checked={config.ui.minimizeToTray}
                  onChange={(e) => updateConfigValue('ui.minimizeToTray', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="minimizeToTray" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Minimize to system tray
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoSave"
                  checked={config.ui.autoSave}
                  onChange={(e) => updateConfigValue('ui.autoSave', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="autoSave" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Auto-save downloads
                </label>
              </div>
            </div>
          )}

          {activeTab === 'downloads' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Concurrent Downloads
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={config.downloads.maxConcurrent}
                  onChange={(e) => updateConfigValue('downloads.maxConcurrent', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Default Quality
                </label>
                <select
                  value={config.downloads.defaultQuality}
                  onChange={(e) => updateConfigValue('downloads.defaultQuality', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                >
                  {QUALITY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Retry Attempts
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  value={config.downloads.retryAttempts}
                  onChange={(e) => updateConfigValue('downloads.retryAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Timeout (seconds)
                </label>
                <input
                  type="number"
                  min="10"
                  max="300"
                  value={config.downloads.timeoutSeconds}
                  onChange={(e) => updateConfigValue('downloads.timeoutSeconds', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          )}

          {activeTab === 'network' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  User Agent
                </label>
                <input
                  type="text"
                  value={config.network.userAgent}
                  onChange={(e) => updateConfigValue('network.userAgent', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Connection Timeout (ms)
                </label>
                <input
                  type="number"
                  min="1000"
                  max="60000"
                  value={config.network.connectionTimeout}
                  onChange={(e) => updateConfigValue('network.connectionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Bandwidth (Mbps, 0 = unlimited)
                </label>
                <input
                  type="number"
                  min="0"
                  value={config.network.maxBandwidthMbps}
                  onChange={(e) => updateConfigValue('network.maxBandwidthMbps', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          )}

          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Log Level
                </label>
                <select
                  value={config.logging.level}
                  onChange={(e) => updateConfigValue('logging.level', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                >
                  <option value="debug">Debug</option>
                  <option value="info">Info</option>
                  <option value="warn">Warning</option>
                  <option value="error">Error</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enablePlugins"
                  checked={config.plugins.enabled}
                  onChange={(e) => updateConfigValue('plugins.enabled', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="enablePlugins" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Enable plugins
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="sanitizeFilenames"
                  checked={config.security.sanitizeFilenames}
                  onChange={(e) => updateConfigValue('security.sanitizeFilenames', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="sanitizeFilenames" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Sanitize filenames
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="validateUrls"
                  checked={config.security.validateUrls}
                  onChange={(e) => updateConfigValue('security.validateUrls', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="validateUrls" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Validate URLs
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            disabled={isLoading}
            className="px-4 py-2 text-red-600 hover:text-red-700 disabled:opacity-50"
          >
            Reset to Defaults
          </button>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 text-gray-600 hover:text-gray-700 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
