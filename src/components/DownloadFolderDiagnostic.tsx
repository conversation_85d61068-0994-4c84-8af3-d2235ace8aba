import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { 
  diagnoseDownloadFolder, 
  testDownloadFunctionality, 
  setupDownloadFolder,
  exportDiagnosticInfo,
  DownloadFolderDiagnostic 
} from '../utils/downloadFolderTest';
import { openFolderInExplorer, selectDownloadFolder } from '../utils/folderUtils';
import { updateConfig } from '../config/configManager';

interface DownloadFolderDiagnosticProps {
  isOpen: boolean;
  onClose: () => void;
  onFolderFixed?: (path: string) => void;
}

const DownloadFolderDiagnosticComponent: React.FC<DownloadFolderDiagnosticProps> = ({ 
  isOpen, 
  onClose, 
  onFolderFixed 
}) => {
  const [diagnostic, setDiagnostic] = useState<DownloadFolderDiagnostic | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isFixing, setIsFixing] = useState(false);

  useEffect(() => {
    if (isOpen) {
      runDiagnostic();
    }
  }, [isOpen]);

  const runDiagnostic = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🔍 Running download folder diagnostic...');
      const result = await diagnoseDownloadFolder();
      setDiagnostic(result);
      
      if (result.status === 'success') {
        toast.success('Download folder is working correctly!');
      } else if (result.status === 'warning') {
        toast.warning(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      toast.error('Failed to run diagnostic');
    } finally {
      setIsRunning(false);
    }
  };

  const runFullTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🧪 Running full download functionality test...');
      const result = await testDownloadFunctionality();
      setTestResults(result.details);
      
      if (result.success) {
        toast.success('All download functionality tests passed!');
        if (onFolderFixed) {
          onFolderFixed(result.path);
        }
      } else {
        toast.error('Download functionality test failed');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      toast.error('Failed to run test');
    } finally {
      setIsRunning(false);
    }
  };

  const fixDownloadFolder = async () => {
    setIsFixing(true);
    
    try {
      console.log('🔧 Attempting to fix download folder...');
      const fixedPath = await setupDownloadFolder();
      
      // Update configuration with the fixed path
      await updateConfig({
        paths: {
          defaultDownloadDir: fixedPath,
          configDir: '',
          logDir: '',
          tempDir: '',
          pluginDir: ''
        }
      });
      
      toast.success(`Download folder fixed: ${fixedPath}`);
      
      if (onFolderFixed) {
        onFolderFixed(fixedPath);
      }
      
      // Re-run diagnostic to confirm fix
      await runDiagnostic();
    } catch (error) {
      console.error('❌ Fix failed:', error);
      toast.error('Failed to fix download folder');
    } finally {
      setIsFixing(false);
    }
  };

  const selectNewFolder = async () => {
    try {
      const selectedPath = await selectDownloadFolder();
      if (selectedPath) {
        // Update configuration
        await updateConfig({
          paths: {
            defaultDownloadDir: selectedPath,
            configDir: '',
            logDir: '',
            tempDir: '',
            pluginDir: ''
          }
        });
        
        toast.success(`Download folder updated: ${selectedPath}`);
        
        if (onFolderFixed) {
          onFolderFixed(selectedPath);
        }
        
        // Re-run diagnostic
        await runDiagnostic();
      }
    } catch (error) {
      console.error('❌ Folder selection failed:', error);
      toast.error('Failed to select folder');
    }
  };

  const openCurrentFolder = async () => {
    if (diagnostic?.path) {
      try {
        await openFolderInExplorer(diagnostic.path);
      } catch (error) {
        console.error('❌ Failed to open folder:', error);
        toast.error('Failed to open folder');
      }
    }
  };

  const exportDiagnostic = async () => {
    try {
      const diagnosticInfo = await exportDiagnosticInfo();
      
      // Create and download the diagnostic file
      const blob = new Blob([diagnosticInfo], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `flowdownload-diagnostic-${new Date().toISOString().slice(0, 19)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Diagnostic information exported');
    } catch (error) {
      console.error('❌ Export failed:', error);
      toast.error('Failed to export diagnostic');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-4/5 h-4/5 max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Download Folder Diagnostic
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Current Status */}
          {diagnostic && (
            <div className={`p-4 rounded-lg mb-6 ${
              diagnostic.status === 'success' ? 'bg-green-100 dark:bg-green-900' :
              diagnostic.status === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900' :
              'bg-red-100 dark:bg-red-900'
            }`}>
              <div className="flex items-center mb-2">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  diagnostic.status === 'success' ? 'bg-green-500' :
                  diagnostic.status === 'warning' ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}></div>
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {diagnostic.status === 'success' ? 'Working' :
                   diagnostic.status === 'warning' ? 'Warning' : 'Error'}
                </h3>
              </div>
              <p className="text-gray-700 dark:text-gray-300 mb-2">{diagnostic.message}</p>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p><strong>Path:</strong> {diagnostic.path}</p>
                <p><strong>Exists:</strong> {diagnostic.exists ? 'Yes' : 'No'}</p>
                <p><strong>Writable:</strong> {diagnostic.canWrite ? 'Yes' : 'No'}</p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button
              onClick={runDiagnostic}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isRunning ? 'Running...' : 'Run Diagnostic'}
            </button>
            
            <button
              onClick={runFullTest}
              disabled={isRunning}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              Full Test
            </button>
            
            <button
              onClick={fixDownloadFolder}
              disabled={isFixing || isRunning}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {isFixing ? 'Fixing...' : 'Auto Fix'}
            </button>
            
            <button
              onClick={selectNewFolder}
              disabled={isRunning}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
            >
              Select Folder
            </button>
          </div>

          {/* Additional Actions */}
          <div className="flex gap-4 mb-6">
            <button
              onClick={openCurrentFolder}
              disabled={!diagnostic?.path || isRunning}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
            >
              Open Folder
            </button>
            
            <button
              onClick={exportDiagnostic}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              Export Diagnostic
            </button>
          </div>

          {/* Suggestions */}
          {diagnostic?.suggestions && diagnostic.suggestions.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                Suggested Alternative Paths:
              </h4>
              <div className="space-y-2">
                {diagnostic.suggestions.map((suggestion, index) => (
                  <div key={index} className="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    <code className="text-sm text-gray-800 dark:text-gray-200">{suggestion}</code>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResults.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                Test Results:
              </h4>
              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm text-gray-700 dark:text-gray-300 mb-1">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end items-center p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DownloadFolderDiagnosticComponent;
