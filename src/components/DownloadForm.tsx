import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { useDownloadStore } from '../store/downloadStore';
import { addDemoDownload } from '../utils/demoDownloads';
import { selectDownloadFolder, formatPathForDisplay, validateDownloadPath, resolvePath } from '../utils/folderUtils';

const LOCAL_STORAGE_DOWNLOAD_PATH_KEY = 'flowdownload_defaultDownloadPath';

const DownloadForm: React.FC = () => {
  const [url, setUrl] = useState('');
  const [filename, setFilename] = useState('');
  const [quality, setQuality] = useState('auto');
  const [currentDownloadPath, setCurrentDownloadPath] = useState<string>(''); // Renamed for clarity
  const [isLoadingPath, setIsLoadingPath] = useState(true);
  const [isSelectingFolder, setIsSelectingFolder] = useState(false);
  const { addDownload, defaultDownloadPath, setDefaultDownloadPath, initializeDefaultPath, isInitialized } = useDownloadStore();

  const qualityOptions = [
    { value: 'auto', label: 'Auto (Best Available)' },
    { value: '2160p', label: '4K (2160p)' },
    { value: '1440p', label: '2K (1440p)' },
    { value: '1080p', label: 'Full HD (1080p)' },
    { value: '720p', label: 'HD (720p)' },
    { value: '480p', label: 'SD (480p)' },
    { value: '360p', label: '360p' },
    { value: '240p', label: '240p' },
    { value: '144p', label: '144p' },
  ];

  // Load and initialize download path
  const initializePath = useCallback(async () => {
    setIsLoadingPath(true);
    
    // First, ensure the store's default path is initialized
    if (!isInitialized) {
      console.log('Initializing store default path...');
      await initializeDefaultPath();
    }
    
    const storedPath = localStorage.getItem(LOCAL_STORAGE_DOWNLOAD_PATH_KEY);
    if (storedPath) {
      const isValid = await validateDownloadPath(storedPath);
      if (isValid) {
        setCurrentDownloadPath(storedPath);
        if (storedPath !== defaultDownloadPath) {
          setDefaultDownloadPath(storedPath);
        }
        setIsLoadingPath(false);
        return;
      }
      toast.warn('Previously saved download path is invalid. Reverting to default.');
      localStorage.removeItem(LOCAL_STORAGE_DOWNLOAD_PATH_KEY); // Remove invalid stored path
    }

    // Use the store's initialized default path
    if (defaultDownloadPath && defaultDownloadPath !== 'Downloads') {
        const isValid = await validateDownloadPath(defaultDownloadPath);
        if (isValid) {
            setCurrentDownloadPath(defaultDownloadPath);
            localStorage.setItem(LOCAL_STORAGE_DOWNLOAD_PATH_KEY, defaultDownloadPath);
            setIsLoadingPath(false);
            return;
        }
    }

    // If still no valid path, set to store's default (even if it's just "Downloads")
    setCurrentDownloadPath(defaultDownloadPath);
    setIsLoadingPath(false);
  }, [defaultDownloadPath, setDefaultDownloadPath, initializeDefaultPath, isInitialized]);

  useEffect(() => {
    initializePath();
  }, [initializePath]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url.trim()) {
      toast.error('Please enter a valid URL');
      return;
    }

    if (!currentDownloadPath.trim()) {
      toast.error('Please select a valid download location.');
      return;
    }

    const finalFilename = filename.trim()
      ? sanitizeFilename(filename.trim()) || 'download.file'
      : extractFilenameFromUrl(url);
    
    try {
      // Make sure the path is resolved properly
      const resolvedPath = await resolvePath(currentDownloadPath);
      console.log('Starting download with resolved path:', resolvedPath);
      
      // Validate that we can write to this path
      const isValid = await validateDownloadPath(resolvedPath);
      if (!isValid) {
        toast.error('Cannot write to the selected download location. Please choose another.');
        return;
      }
      
      // Add the download with the resolved path
      addDownload(url.trim(), finalFilename, quality, resolvedPath);
      
      setUrl('');
      setFilename('');
      
      if (isVideoUrl(url)) {
        toast.success(`Video download started (${quality})`);
      } else {
        toast.success(`Download started`);
      }
    } catch (error) {
      console.error('Error starting download:', error);
      toast.error('Failed to start download. Please check path permissions.');
    }
  };

  const handleSelectFolder = async () => {
    setIsSelectingFolder(true);
    try {
      const selectedPath = await selectDownloadFolder();
      if (selectedPath) {
        const isValid = await validateDownloadPath(selectedPath);
        if (isValid) {
          setCurrentDownloadPath(selectedPath);
          setDefaultDownloadPath(selectedPath); 
          localStorage.setItem(LOCAL_STORAGE_DOWNLOAD_PATH_KEY, selectedPath); 
          toast.success('Download folder updated and saved.');
        } else {
          // validateDownloadPath should have shown a toast, but we can add another if desired
          toast.error('The selected folder is not valid. Please choose a different one.');
        }
      }
      // If selectedPath is null (user cancelled), do nothing, toast is handled in selectDownloadFolder
    } catch (error) {
      console.error('Error during folder selection process:', error);
      toast.error('An unexpected error occurred while selecting the folder.');
    } finally {
      setIsSelectingFolder(false);
    }
  };

  const handleDemoDownload = async () => {
    if (!currentDownloadPath && !defaultDownloadPath) {
        toast.error('Please select a download location before starting a demo download.');
        return;
    }
    
    try {
      // Make sure the path is resolved properly
      const pathForDemo = currentDownloadPath || defaultDownloadPath;
      const resolvedPath = await resolvePath(pathForDemo);
      console.log('Starting demo download with resolved path:', resolvedPath);
      
      // Validate that we can write to this path
      const isValid = await validateDownloadPath(resolvedPath);
      if (!isValid) {
        toast.error('Cannot write to the selected download location. Please choose another.');
        return;
      }
      
      addDemoDownload(resolvedPath);
      toast.success('Demo download started!');
    } catch (error) {
      console.error('Error starting demo download:', error);
      toast.error('Failed to start demo download. Please check path permissions.');
    }
  };

  const sanitizeFilename = (filename: string): string => {
    // Remove or replace invalid characters for cross-platform compatibility
    return filename
      .replace(/[<>:"/\\|?*\u0000-\u001f]/g, '_') // Replace invalid chars with underscore
      .replace(/^\.+/, '') // Remove leading dots
      .replace(/\.+$/, '') // Remove trailing dots
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 255); // Limit length to 255 chars
  };

  const extractFilenameFromUrl = (url: string): string => {
    try {
      const pathname = new URL(url).pathname;
      const filenamePart = pathname.split('/').pop();
      if (!filenamePart) return 'download.file';

      // Sanitize the filename
      let sanitized = sanitizeFilename(filenamePart);
      if (!sanitized) sanitized = 'download';

      // Ensure it has an extension or add a default one
      return sanitized.includes('.') ? sanitized : `${sanitized}.file`;
    } catch {
      return 'download.file'; // Fallback for invalid URLs
    }
  };

  const isVideoUrl = (url: string): boolean => {
    const videoSites = [
      'youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com', 
      'twitch.tv', 'tiktok.com', 'instagram.com', 'facebook.com'
    ];
    try {
      const hostname = new URL(url).hostname.toLowerCase();
      return videoSites.some(site => hostname.includes(site));
    } catch {
      return false;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          Add New Download
        </h2>
        <button
          onClick={handleDemoDownload}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium flex items-center"
          disabled={isLoadingPath}
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-3-8v16" />
          </svg>
          Try Demo
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
            URL
          </label>
          <input
            type="url"
            id="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="https://www.youtube.com/watch?v=..."
            required
          />
          {url && isVideoUrl(url) && (
            <p className="text-xs text-green-600 mt-1 flex items-center">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Video platform detected
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="filename" className="block text-sm font-medium text-gray-700 mb-1">
              Filename (optional)
            </label>
            <input
              type="text"
              id="filename"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Custom filename..."
            />
          </div>

          <div>
            <label htmlFor="quality" className="block text-sm font-medium text-gray-700 mb-1">
              Quality
            </label>
            <select
              id="quality"
              value={quality}
              onChange={(e) => setQuality(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white"
            >
              {qualityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Download Location
          </label>
          <div className="flex gap-2">
            <div className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 flex items-center min-h-[38px]">
              <svg className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h14a2 2 0 002 2v2" />
              </svg>
              <span className="truncate" title={currentDownloadPath}>
                {isLoadingPath ? 'Loading path...' : (currentDownloadPath ? formatPathForDisplay(currentDownloadPath, 60) : 'Please select a folder...')}
              </span>
            </div>
            <button
              type="button"
              onClick={handleSelectFolder}
              disabled={isSelectingFolder || isLoadingPath}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
            >
              {isSelectingFolder ? (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              ) : (
                'Browse'
              )}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Files will be saved to this location. This setting is persisted across sessions.
          </p>
        </div>
        
        <button
          type="submit"
          className="btn btn-primary px-6 py-2 h-10 w-full md:w-auto"
          disabled={isLoadingPath || !currentDownloadPath}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Add Download
        </button>
      </form>
    </div>
  );
};

export default DownloadForm; 