import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { withTauriAPIs } from '../utils/tauriUtils';

const YtDlpDiagnostic: React.FC = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [ytdlpStatus, setYtdlpStatus] = useState<{
    available: boolean;
    path?: string;
    error?: string;
  } | null>(null);

  const checkYtDlpStatus = async () => {
    setIsChecking(true);
    try {
      // First, let's bypass the normal detection and try to import Tauri APIs directly
      console.log('🔍 Starting yt-dlp diagnostic...');
      
      try {
        console.log('📦 Attempting direct Tauri API import...');
        const { invoke } = await import('@tauri-apps/api/core');
        
        console.log('✅ Tauri core API imported, testing yt-dlp availability...');
        
        // Check availability directly
        const available = await invoke('check_ytdlp_availability');
        console.log('🎯 yt-dlp availability result:', available);
        
        if (available) {
          // Get path
          try {
            const path = await invoke('get_ytdlp_executable_path') as string;
            console.log('📍 yt-dlp path found:', path);
            setYtdlpStatus({ available: true, path });
            toast.success(`yt-dlp found at: ${path}`);
          } catch (pathError) {
            console.error('❌ Path detection failed:', pathError);
            setYtdlpStatus({ available: true, error: 'Path detection failed' });
            toast.warning('yt-dlp is available but path detection failed');
          }
        } else {
          setYtdlpStatus({ available: false });
          toast.error('yt-dlp is not available on this system');
        }
      } catch (directError) {
        console.error('❌ Direct Tauri API import failed:', directError);
        
        // Fallback to the withTauriAPIs method
        await withTauriAPIs(
          async ({ invoke }) => {
            console.log('🔄 Using fallback API method...');
            const available = await invoke('check_ytdlp_availability');
            console.log('yt-dlp available:', available);
            
            if (available) {
              try {
                const path = await invoke('get_ytdlp_executable_path');
                console.log('yt-dlp path:', path);
                setYtdlpStatus({ available: true, path });
                toast.success(`yt-dlp found at: ${path}`);
              } catch (pathError) {
                setYtdlpStatus({ available: true, error: 'Path detection failed' });
                toast.warning('yt-dlp is available but path detection failed');
              }
            } else {
              setYtdlpStatus({ available: false });
              toast.error('yt-dlp is not available');
            }
          },
          () => {
            setYtdlpStatus({ available: false, error: 'Desktop app features not available' });
            toast.error('Cannot access desktop app features. Please ensure you are running the Tauri desktop application.');
          }
        );
      }
    } catch (error) {
      console.error('❌ yt-dlp check completely failed:', error);
      setYtdlpStatus({ available: false, error: error instanceof Error ? error.message : 'Unknown error' });
      toast.error(`Failed to check yt-dlp status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsChecking(false);
    }
  };

  const testYtDlpCommand = async () => {
    if (!ytdlpStatus?.path) {
      toast.error('yt-dlp path not available');
      return;
    }

    try {
      const { invoke } = await import('@tauri-apps/api/core');
      
      toast.info('Testing yt-dlp execution...');
      const testResult = await invoke('test_ytdlp_command') as string;
      
      console.log('✅ yt-dlp test result:', testResult);
      toast.success(`yt-dlp test successful: ${testResult}`);
      
    } catch (error) {
      console.error('❌ yt-dlp test failed:', error);
      toast.error(`yt-dlp test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">yt-dlp Diagnostic</h3>
      
      <div className="space-y-4">
        <button
          onClick={checkYtDlpStatus}
          disabled={isChecking}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isChecking ? (
            <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          ) : (
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
          Check yt-dlp Status
        </button>

        {ytdlpStatus && (
          <div className={`p-4 rounded-md ${ytdlpStatus.available ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center">
              {ytdlpStatus.available ? (
                <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
              <span className={`font-medium ${ytdlpStatus.available ? 'text-green-800' : 'text-red-800'}`}>
                {ytdlpStatus.available ? 'yt-dlp Available' : 'yt-dlp Not Available'}
              </span>
            </div>
            
            {ytdlpStatus.path && (
              <div className="mt-2">
                <span className="text-sm text-green-700">Path: </span>
                <code className="text-sm bg-green-100 px-1 rounded">{ytdlpStatus.path}</code>
              </div>
            )}
            
            {ytdlpStatus.error && (
              <div className="mt-2">
                <span className="text-sm text-red-700">Error: </span>
                <span className="text-sm text-red-600">{ytdlpStatus.error}</span>
              </div>
            )}
          </div>
        )}

        {ytdlpStatus?.available && ytdlpStatus.path && (
          <button
            onClick={testYtDlpCommand}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-3-8v16" />
            </svg>
            Test yt-dlp Command
          </button>
        )}

        {!ytdlpStatus?.available && ytdlpStatus && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="font-medium text-blue-800 mb-2">Installation Instructions</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>macOS:</strong> <code>brew install yt-dlp</code></p>
              <p><strong>Windows:</strong> <code>pip install yt-dlp</code></p>
              <p><strong>Linux:</strong> <code>sudo apt install yt-dlp</code> or <code>pip install yt-dlp</code></p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default YtDlpDiagnostic;