import React from 'react';
import { useDownloadStore } from '../store/downloadStore';

interface DeviceOptimizationProps {
  onSelectDevice?: (device: 'desktop' | 'mobile' | 'audio') => void;
}

const DeviceOptimization: React.FC<DeviceOptimizationProps> = ({ onSelectDevice }) => {
  // Get all current downloads
  const downloads = useDownloadStore((state) => state.downloads);
  
  // Calculate statistics for each device type based on existing downloads
  const desktopDownloads = downloads.filter(d => 
    d.quality.includes('1080p') || 
    d.quality.includes('1440p') || 
    d.quality.includes('2160p') || 
    d.quality.includes('4K')
  ).length;
  
  const mobileDownloads = downloads.filter(d => 
    d.quality.includes('720p') || 
    d.quality.includes('480p') || 
    d.quality.includes('360p')
  ).length;
  
  const audioDownloads = downloads.filter(d => 
    d.quality.includes('Audio') || 
    d.quality.toLowerCase().includes('audio')
  ).length;
  
  // Handle device selection
  const handleDeviceSelect = (device: 'desktop' | 'mobile' | 'audio') => {
    if (onSelectDevice) {
      onSelectDevice(device);
    } else {
      // Default quality presets based on device
      let quality = '';
      
      switch(device) {
        case 'desktop':
          quality = '1080p';
          break;
        case 'mobile':
          quality = '480p';
          break;
        case 'audio':
          quality = 'Audio Only (High)';
          break;
      }
      
      // Example only - we'd need to connect this properly to the actual form
      console.log(`Selected device: ${device} with quality: ${quality}`);
    }
  };
  
  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Device Optimization</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Desktop Option */}
        <div 
          className="border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-700 rounded-lg p-5 hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-colors cursor-pointer"
          onClick={() => handleDeviceSelect('desktop')}
        >
          <div className="flex items-center justify-center mb-4 text-blue-600 dark:text-blue-400">
            <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-center text-gray-900 dark:text-white font-medium mb-2">Desktop</h3>
          <p className="text-center text-xs text-gray-600 dark:text-gray-400">Ultra-high quality downloads optimized for large screens</p>
          
          <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-500 dark:text-gray-400">Presets:</span>
              <span className="font-medium text-blue-600 dark:text-blue-400">1080p - 4K</span>
            </div>
            {desktopDownloads > 0 && (
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
                <span>Downloads:</span>
                <span className="font-medium">{desktopDownloads}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Mobile Option */}
        <div 
          className="border border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-700 rounded-lg p-5 hover:bg-green-50 dark:hover:bg-green-900/10 transition-colors cursor-pointer"
          onClick={() => handleDeviceSelect('mobile')}
        >
          <div className="flex items-center justify-center mb-4 text-green-600 dark:text-green-400">
            <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-center text-gray-900 dark:text-white font-medium mb-2">Mobile</h3>
          <p className="text-center text-xs text-gray-600 dark:text-gray-400">Compressed formats perfect for mobile viewing</p>
          
          <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-500 dark:text-gray-400">Presets:</span>
              <span className="font-medium text-green-600 dark:text-green-400">360p - 720p</span>
            </div>
            {mobileDownloads > 0 && (
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
                <span>Downloads:</span>
                <span className="font-medium">{mobileDownloads}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Audio Option */}
        <div 
          className="border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-700 rounded-lg p-5 hover:bg-purple-50 dark:hover:bg-purple-900/10 transition-colors cursor-pointer"
          onClick={() => handleDeviceSelect('audio')}
        >
          <div className="flex items-center justify-center mb-4 text-purple-600 dark:text-purple-400">
            <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 010-7.072m12.728 0l-3.182-3.182a1 1 0 00-1.414 0l-3.182 3.182m7.778 0L15 12m-6.364 0L12 15.364m0-3.364l-1.5 1.5-1.5-1.5m-1.5-1.5L6 12.5l1.5 1.5m4.5-4.5l1.5-1.5 1.5 1.5" />
            </svg>
          </div>
          <h3 className="text-center text-gray-900 dark:text-white font-medium mb-2">Audio</h3>
          <p className="text-center text-xs text-gray-600 dark:text-gray-400">Extract high-quality audio in multiple formats</p>
          
          <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-500 dark:text-gray-400">Formats:</span>
              <span className="font-medium text-purple-600 dark:text-purple-400">MP3, AAC, FLAC</span>
            </div>
            {audioDownloads > 0 && (
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
                <span>Downloads:</span>
                <span className="font-medium">{audioDownloads}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceOptimization; 