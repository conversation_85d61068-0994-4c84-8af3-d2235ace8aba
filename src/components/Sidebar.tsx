import React from 'react';

const Sidebar: React.FC = () => {
  return (
    <div className="h-full p-4">
      {/* Content Type */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Content Type</h3>
        
        {/* Video Content */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-3">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">Video Content</h4>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">HD/4K Video Downloads</p>
            </div>
          </div>
        </div>
        
        {/* Audio Files */}
        <div className="border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-4 mb-3 transition-colors">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Audio Files</h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Music & Podcasts</p>
            </div>
          </div>
        </div>
        
        {/* Images */}
        <div className="border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-4 mb-3 transition-colors">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Images</h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Photos & Graphics</p>
            </div>
          </div>
        </div>
        
        {/* Documents */}
        <div className="border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-4 transition-colors">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Documents</h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">PDFs & Files</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Session Statistics */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Session Statistics</h3>
        
        <div className="space-y-4">
          {/* Downloads */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              <span className="text-sm">Downloads</span>
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">12</span>
          </div>
          
          {/* Data Saved */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
              </svg>
              <span className="text-sm">Data Saved</span>
            </div>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">3.7 GB</span>
          </div>
          
          {/* Success Rate */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm">Success Rate</span>
            </div>
            <span className="text-sm font-medium text-purple-600 dark:text-purple-400">98%</span>
          </div>
          
          {/* Avg Speed */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span className="text-sm">Avg Speed</span>
            </div>
            <span className="text-sm font-medium text-orange-600 dark:text-orange-400">45 MB/s</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 