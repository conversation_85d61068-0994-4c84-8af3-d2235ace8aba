import React from 'react';
import { toast } from 'react-toastify';

const QuickActions: React.FC = () => {
  const handleOpenDownloadsFolder = async () => {
    try {
      const { open } = await import('@tauri-apps/plugin-shell');
      const { invoke } = await import('@tauri-apps/api/core');
      
      // Get the downloads directory path
      const downloadsPath = await invoke<string>('get_download_dir');
      
      // Open the folder
      await open(downloadsPath);
      toast.success('Opening downloads folder');
    } catch (error) {
      console.error('Error opening downloads folder:', error);
      toast.error('Could not open downloads folder');
    }
  };
  
  const handlePreferences = () => {
    toast.info('Preferences dialog would open here');
  };
  
  const handleViewStatistics = () => {
    toast.info('Statistics view would open here');
  };
  
  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
      
      <div className="space-y-2">
        {/* Open Downloads Folder */}
        <button 
          onClick={handleOpenDownloadsFolder}
          className="flex items-center w-full px-4 py-3 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-200 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
        >
          <svg className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
          </svg>
          Open Downloads Folder
        </button>
        
        {/* Preferences */}
        <button 
          onClick={handlePreferences}
          className="flex items-center w-full px-4 py-3 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-200 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
        >
          <svg className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Preferences
        </button>
        
        {/* View Statistics */}
        <button 
          onClick={handleViewStatistics}
          className="flex items-center w-full px-4 py-3 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-200 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
        >
          <svg className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          View Statistics
        </button>
      </div>
    </div>
  );
};

export default QuickActions; 