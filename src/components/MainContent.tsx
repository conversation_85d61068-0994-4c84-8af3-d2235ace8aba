import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import { useDownloadStore } from '../store/downloadStore';
import { selectDownloadFolder, validateDownloadPath } from '../utils/folderUtils';
import DeviceOptimization from './DeviceOptimization';

// Quality options for different media types
const VIDEO_QUALITIES = [
  { label: 'Best Quality Available', value: 'best', icon: '🔴', description: 'Automatically selects highest quality', size: 'Variable', color: 'red' },
  { label: '4K (2160p)', value: '2160p', icon: '4K', description: 'Ultra HD resolution', size: '~3.5 GB', color: 'purple' },
  { label: '1440p', value: '1440p', icon: 'QHD', description: 'Quad HD resolution', size: '~1.8 GB', color: 'indigo' },
  { label: '1080p', value: '1080p', icon: 'HD', description: 'Full HD resolution', size: '~800 MB', color: 'blue' },
  { label: '720p', value: '720p', icon: 'HD', description: 'HD resolution', size: '~350 MB', color: 'teal' },
  { label: '480p', value: '480p', icon: 'SD', description: 'Standard definition', size: '~150 MB', color: 'green' },
  { label: '360p', value: '360p', icon: 'SD', description: 'Low resolution', size: '~80 MB', color: 'yellow' },
  { label: '240p', value: '240p', icon: 'LD', description: 'Very low resolution', size: '~40 MB', color: 'orange' },
  { label: 'Audio Only (High)', value: 'audio-high', icon: '🎵', description: 'High quality audio (320kbps)', size: '~10 MB', color: 'pink' },
  { label: 'Audio Only (Medium)', value: 'audio-medium', icon: '🎵', description: 'Medium quality audio (192kbps)', size: '~7 MB', color: 'pink' },
  { label: 'Audio Only (Low)', value: 'audio-low', icon: '🎵', description: 'Low quality audio (128kbps)', size: '~5 MB', color: 'pink' },
];

// Format options
const FORMAT_OPTIONS = [
  { label: 'MP4', value: 'mp4', description: 'Best compatibility across devices' },
  { label: 'MKV', value: 'mkv', description: 'Best for high quality and subtitles' },
  { label: 'WebM', value: 'webm', description: 'Optimized for web viewing' },
  { label: 'MP3', value: 'mp3', description: 'Audio only (MP3 format)' },
  { label: 'AAC', value: 'aac', description: 'High quality audio (AAC format)' },
  { label: 'FLAC', value: 'flac', description: 'Lossless audio format' },
];

const MainContent: React.FC = () => {
  const { addDownload, defaultDownloadPath } = useDownloadStore();
  const [url, setUrl] = useState('');
  const [selectedQuality, setSelectedQuality] = useState('best');
  const [selectedFormat, setSelectedFormat] = useState('mp4');
  const [customFilename, setCustomFilename] = useState('');
  const [downloadPath, setDownloadPath] = useState(defaultDownloadPath);
  const [isLoading, setIsLoading] = useState(false);
  const [showQualityDropdown, setShowQualityDropdown] = useState(false);
  const [showFormatDropdown, setShowFormatDropdown] = useState(false);
  const qualityDropdownRef = useRef<HTMLDivElement>(null);
  const formatDropdownRef = useRef<HTMLDivElement>(null);
  
  // Find the selected quality option
  const selectedQualityOption = VIDEO_QUALITIES.find(q => q.value === selectedQuality) || VIDEO_QUALITIES[0];
  const selectedFormatOption = FORMAT_OPTIONS.find(f => f.value === selectedFormat) || FORMAT_OPTIONS[0];
  
  // Handle device optimization selection
  const handleDeviceSelect = (device: 'desktop' | 'mobile' | 'audio') => {
    // Update quality and format based on device type
    switch(device) {
      case 'desktop':
        setSelectedQuality('1080p');
        setSelectedFormat('mp4');
        toast.info('Desktop optimization selected: 1080p MP4');
        break;
      case 'mobile':
        setSelectedQuality('480p');
        setSelectedFormat('mp4');
        toast.info('Mobile optimization selected: 480p MP4');
        break;
      case 'audio':
        setSelectedQuality('audio-high');
        setSelectedFormat('mp3');
        toast.info('Audio optimization selected: High Quality MP3');
        break;
    }
  };
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (qualityDropdownRef.current && !qualityDropdownRef.current.contains(event.target as Node)) {
        setShowQualityDropdown(false);
      }
      if (formatDropdownRef.current && !formatDropdownRef.current.contains(event.target as Node)) {
        setShowFormatDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleBrowse = async () => {
    const selectedPath = await selectDownloadFolder();
    if (selectedPath) {
      setDownloadPath(selectedPath);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      if (!url) {
        toast.error('Please enter a URL');
        setIsLoading(false);
        return;
      }
      
      // Validate download path
      if (!downloadPath) {
        toast.error('Please select a download path');
        setIsLoading(false);
        return;
      }
      
      const isPathValid = await validateDownloadPath(downloadPath);
      if (!isPathValid) {
        toast.error('Download path is invalid or not accessible');
        setIsLoading(false);
        return;
      }
      
      // Generate filename if not provided
      let filename = customFilename;
      if (!filename) {
        try {
          const urlObj = new URL(url);
          const pathSegments = urlObj.pathname.split('/').filter(Boolean);
          if (pathSegments.length > 0) {
            filename = pathSegments[pathSegments.length - 1];
          } else {
            filename = `download_${Date.now()}.${selectedFormat}`;
          }
        } catch (e) {
          filename = `download_${Date.now()}.${selectedFormat}`;
        }
      }
      
      // Ensure filename has correct extension
      if (!filename.endsWith(`.${selectedFormat}`) && !selectedFormat.includes('audio')) {
        filename = `${filename}.${selectedFormat}`;
      } else if (selectedFormat.includes('audio')) {
        // Handle audio formats
        const audioExt = selectedFormat === 'audio-high' || selectedFormat === 'audio-medium' || selectedFormat === 'audio-low' 
          ? 'mp3' 
          : selectedFormat.replace('audio-', '');
        
        if (!filename.endsWith(`.${audioExt}`)) {
          filename = `${filename}.${audioExt}`;
        }
      }
      
      // Add download with quality and format information
      addDownload(url, filename, selectedQualityOption.label, downloadPath);
      // Sanitize filename for display to prevent XSS
      const safeFilename = filename.replace(/[<>&"']/g, '');
      toast.success(`Added download: ${safeFilename}`);
      
      // Reset form
      setUrl('');
      setCustomFilename('');
      
    } catch (error) {
      console.error('Error adding download:', error);
      if (error instanceof Error) {
        // Sanitize error message to prevent XSS
        const safeErrorMessage = error.message.replace(/[<>&"']/g, '');
        toast.error(`Error: ${safeErrorMessage}`);
      } else {
        toast.error('An unknown error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Helper function to get color classes based on quality option
  const getColorClasses = (color: string) => {
    const colorMap: Record<string, {bg: string, darkBg: string, text: string, darkText: string}> = {
      red: {bg: 'bg-red-100', darkBg: 'dark:bg-red-900/30', text: 'text-red-600', darkText: 'dark:text-red-400'},
      purple: {bg: 'bg-purple-100', darkBg: 'dark:bg-purple-900/30', text: 'text-purple-600', darkText: 'dark:text-purple-400'},
      indigo: {bg: 'bg-indigo-100', darkBg: 'dark:bg-indigo-900/30', text: 'text-indigo-600', darkText: 'dark:text-indigo-400'},
      blue: {bg: 'bg-blue-100', darkBg: 'dark:bg-blue-900/30', text: 'text-blue-600', darkText: 'dark:text-blue-400'},
      teal: {bg: 'bg-teal-100', darkBg: 'dark:bg-teal-900/30', text: 'text-teal-600', darkText: 'dark:text-teal-400'},
      green: {bg: 'bg-green-100', darkBg: 'dark:bg-green-900/30', text: 'text-green-600', darkText: 'dark:text-green-400'},
      yellow: {bg: 'bg-yellow-100', darkBg: 'dark:bg-yellow-900/30', text: 'text-yellow-600', darkText: 'dark:text-yellow-400'},
      orange: {bg: 'bg-orange-100', darkBg: 'dark:bg-orange-900/30', text: 'text-orange-600', darkText: 'dark:text-orange-400'},
      pink: {bg: 'bg-pink-100', darkBg: 'dark:bg-pink-900/30', text: 'text-pink-600', darkText: 'dark:text-pink-400'},
    };
    
    return colorMap[color] || colorMap.blue;
  };
  
  const colors = getColorClasses(selectedQualityOption.color);
  
  return (
    <div className="space-y-8">
      {/* Add New Download Form */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Add New Download</h2>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <svg className="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Configure your download settings and start downloading</span>
          </div>
        </div>
        
        <form onSubmit={handleSubmit}>
          {/* Source URL */}
          <div className="mb-5">
            <label htmlFor="source-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Source URL
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <input
                type="text"
                id="source-url"
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100"
                placeholder="https://www.youtube.com/watch?v=example or any media URL..."
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
            {/* Quality Selection */}
            <div>
              <label htmlFor="quality" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Quality Selection
              </label>
              <div className="relative" ref={qualityDropdownRef}>
                <div 
                  className="flex items-center border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-slate-700 p-3 cursor-pointer"
                  onClick={() => setShowQualityDropdown(!showQualityDropdown)}
                >
                  <div className="flex-shrink-0 mr-3">
                    <span className={`inline-flex items-center justify-center h-8 w-8 rounded-full ${colors.bg} ${colors.darkBg} ${colors.text} ${colors.darkText}`}>
                      {selectedQualityOption.icon}
                    </span>
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">{selectedQualityOption.label}</span>
                      <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
                
                {/* Quality Dropdown */}
                {showQualityDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {VIDEO_QUALITIES.map((quality) => {
                      const qColors = getColorClasses(quality.color);
                      return (
                        <div 
                          key={quality.value}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-slate-700 cursor-pointer flex items-center"
                          onClick={() => {
                            setSelectedQuality(quality.value);
                            setShowQualityDropdown(false);
                          }}
                        >
                          <span className={`inline-flex items-center justify-center h-6 w-6 rounded-full ${qColors.bg} ${qColors.darkBg} ${qColors.text} ${qColors.darkText} mr-2 text-xs`}>
                            {quality.icon}
                          </span>
                          <div className="flex-grow">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{quality.label}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 flex justify-between">
                              <span>{quality.description}</span>
                              <span className="text-xs font-medium">{quality.size}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                
                <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                  <div className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span>{selectedQualityOption.description}</span>
                  </div>
                  <div className="ml-4 text-gray-500 dark:text-gray-400">Est. size: {selectedQualityOption.size}</div>
                </div>
              </div>
            </div>
            
            {/* Format Selection */}
            <div>
              <label htmlFor="format" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Format Selection
              </label>
              <div className="relative" ref={formatDropdownRef}>
                <div 
                  className="flex items-center border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-slate-700 p-3 cursor-pointer"
                  onClick={() => setShowFormatDropdown(!showFormatDropdown)}
                >
                  <div className="flex-shrink-0 mr-3">
                    <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                      {selectedFormat.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">{selectedFormatOption.label}</span>
                      <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
                
                {/* Format Dropdown */}
                {showFormatDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg overflow-hidden">
                    {FORMAT_OPTIONS.map((format) => (
                      <div 
                        key={format.value}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-slate-700 cursor-pointer"
                        onClick={() => {
                          setSelectedFormat(format.value);
                          setShowFormatDropdown(false);
                        }}
                      >
                        <div className="flex items-center">
                          <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs mr-2">
                            {format.value.toUpperCase()}
                          </span>
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{format.label}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{format.description}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {selectedFormatOption.description}
                </div>
              </div>
            </div>
            
            {/* Custom Filename */}
            <div>
              <label htmlFor="custom-filename" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Custom Filename (Optional)
              </label>
              <input
                type="text"
                id="custom-filename"
                className="block w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter custom filename..."
                value={customFilename}
                onChange={(e) => setCustomFilename(e.target.value)}
              />
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Leave empty to use original filename
              </div>
            </div>
            
            {/* Download Location */}
            <div>
              <label htmlFor="download-location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Download Location
              </label>
              <div className="flex">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h14a2 2 0 002 2v2" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    id="download-location"
                    className="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-700 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100"
                    placeholder="/Users/<USER>/Downloads"
                    value={downloadPath}
                    onChange={(e) => setDownloadPath(e.target.value)}
                  />
                </div>
                <button
                  type="button"
                  onClick={handleBrowse}
                  className="px-4 py-3 border border-gray-300 dark:border-gray-700 border-l-0 bg-gray-50 dark:bg-slate-700 text-gray-700 dark:text-gray-300 font-medium rounded-r-md hover:bg-gray-100 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Browse
                </button>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <button
              type="submit"
              disabled={isLoading}
              className="flex items-center justify-center w-full md:w-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              {isLoading ? 'Processing...' : 'Start Download'}
            </button>
            
            <button
              type="button"
              className="hidden md:flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-md bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
            >
              <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Advanced
            </button>
          </div>
        </form>
      </div>
      
      {/* Device Optimization */}
      <DeviceOptimization onSelectDevice={handleDeviceSelect} />
    </div>
  );
};

export default MainContent; 