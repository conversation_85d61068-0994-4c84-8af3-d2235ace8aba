import React from 'react';
import { useDownloadStore, Download } from '../store/downloadStore';

const RecentActivity: React.FC = () => {
  // Get real downloads from the store
  const downloads = useDownloadStore(state => state.downloads);
  
  // Get formatted file size
  const getFormattedSize = (download: Download) => {
    if (download.fileSize) {
      return download.fileSize;
    }
    if (download.bytesTotal) {
      return formatFileSize(download.bytesTotal);
    }
    return "Unknown size";
  };
  
  // Format file size function
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };
  
  // Get formatted time
  const getFormattedTime = (download: Download) => {
    if (download.status === 'downloading') {
      return download.estimatedTimeRemaining || 'Calculating...';
    }
    
    if (download.status === 'pending') {
      return 'Waiting';
    }
    
    if (download.completedAt) {
      const now = new Date();
      const completedTime = new Date(download.completedAt);
      const diffMs = now.getTime() - completedTime.getTime();
      const diffMins = Math.round(diffMs / 60000);
      
      if (diffMins < 1) {
        return 'Just now';
      } else if (diffMins < 60) {
        return `${diffMins} min ago`;
      } else {
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) {
          return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else {
          const diffDays = Math.floor(diffHours / 24);
          return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        }
      }
    }
    
    return 'Unknown time';
  };
  
  // Extract quality info to display in the UI
  const getQualityInfo = (download: Download) => {
    // Look for resolution patterns
    if (download.quality.includes('2160p') || download.quality.includes('4K')) {
      return { label: '4K', color: 'purple' };
    } else if (download.quality.includes('1440p')) {
      return { label: 'QHD', color: 'indigo' };
    } else if (download.quality.includes('1080p')) {
      return { label: 'FHD', color: 'blue' };
    } else if (download.quality.includes('720p')) {
      return { label: 'HD', color: 'teal' };
    } else if (download.quality.includes('480p')) {
      return { label: 'SD', color: 'green' };
    } else if (download.quality.includes('360p')) {
      return { label: 'LD', color: 'yellow' };
    } else if (download.quality.includes('Audio')) {
      return { label: '🎵', color: 'pink' };
    }
    
    // Default for unknown quality
    return { label: 'Auto', color: 'gray' };
  };
  
  // Get the color classes for quality badges
  const getQualityColor = (color: string) => {
    const colorMap: Record<string, string> = {
      purple: 'bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-300',
      indigo: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/40 dark:text-indigo-300',
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300',
      teal: 'bg-teal-100 text-teal-800 dark:bg-teal-900/40 dark:text-teal-300',
      green: 'bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300',
      yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-300',
      pink: 'bg-pink-100 text-pink-800 dark:bg-pink-900/40 dark:text-pink-300',
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-900/40 dark:text-gray-300',
    };
    
    return colorMap[color] || colorMap.gray;
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'downloading':
        return (
          <svg className="w-5 h-5 text-blue-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };
  
  // If we have no downloads, show a placeholder
  if (downloads.length === 0) {
    return (
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-5 text-center">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            No download activity yet
          </div>
          <div className="mt-2 text-sm">
            <span className="text-blue-600 dark:text-blue-400">Add a download</span> to get started
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
      
      <div className="space-y-4">
        {downloads.slice(0, 5).map(download => {
          const qualityInfo = getQualityInfo(download);
          const qualityColorClass = getQualityColor(qualityInfo.color);
          
          return (
            <div key={download.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-start">
                {/* Status icon */}
                <div className="flex-shrink-0 mr-3 mt-1">
                  {getStatusIcon(download.status)}
                </div>
                
                {/* Download info */}
                <div className="flex-grow min-w-0">
                  <div className="flex items-center">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate mr-2">{download.filename}</h4>
                    <span className={`text-xs px-1.5 py-0.5 rounded ${qualityColorClass}`}>{qualityInfo.label}</span>
                  </div>
                  
                  <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <span>{getFormattedSize(download)}</span>
                    <span className="mx-1">•</span>
                    <span>{getFormattedTime(download)}</span>
                    {download.downloadSpeed && download.status === 'downloading' && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{download.downloadSpeed}</span>
                      </>
                    )}
                  </div>
                  
                  {/* Progress bar for downloading items */}
                  {download.status === 'downloading' && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                        <div
                          className="bg-blue-600 h-1.5 rounded-full"
                          style={{ width: `${download.progress}%` }}
                        />
                      </div>
                      <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {download.progress}% complete
                      </div>
                    </div>
                  )}
                  
                  {/* Error message if present */}
                  {download.status === 'error' && download.error && (
                    <div className="mt-1 text-xs text-red-500">
                      {download.error}
                    </div>
                  )}
                </div>
                
                {/* Status indicator dot/icon */}
                <div className="ml-2 flex-shrink-0">
                  {download.status === 'completed' && (
                    <span className="inline-block w-3 h-3 bg-green-500 rounded-full"></span>
                  )}
                  {download.status === 'downloading' && (
                    <span className="inline-block w-3 h-3 bg-blue-500 rounded-full animate-pulse"></span>
                  )}
                  {download.status === 'pending' && (
                    <span className="inline-block w-3 h-3 bg-yellow-500 rounded-full"></span>
                  )}
                  {download.status === 'error' && (
                    <span className="inline-block w-3 h-3 bg-red-500 rounded-full"></span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default RecentActivity; 