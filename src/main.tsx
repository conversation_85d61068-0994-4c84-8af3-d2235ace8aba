import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

// Prepare for dark mode by adding class to document early
const getInitialTheme = (): 'light' | 'dark' => {
  const savedTheme = localStorage.getItem('theme');
  
  if (savedTheme === 'light' || savedTheme === 'dark') {
    return savedTheme as 'light' | 'dark';
  }
  
  // Check user system preference
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
};

document.documentElement.classList.add(getInitialTheme());

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found. Make sure there is an element with id="root" in your HTML.');
}

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
); 