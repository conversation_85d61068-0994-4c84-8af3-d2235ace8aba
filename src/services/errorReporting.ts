/**
 * Error reporting and logging service
 * Handles error collection, logging, and optional remote reporting
 */

import { AppError, ErrorSeverity, ErrorContext } from '../types/errors';

export interface ErrorReport {
  id: string;
  timestamp: Date;
  error: AppError;
  sessionId: string;
  appVersion: string;
  platform: string;
  resolved?: boolean;
  reportedToRemote?: boolean;
}

export interface ErrorReportingConfig {
  enableRemoteReporting: boolean;
  enableLocalLogging: boolean;
  maxLocalReports: number;
  reportingEndpoint?: string;
  apiKey?: string;
  enableUserConsent: boolean;
}

class ErrorReportingService {
  private config: ErrorReportingConfig;
  private localReports: ErrorReport[] = [];
  private sessionId: string;
  private appVersion: string;
  private platform: string;

  constructor(config: ErrorReportingConfig) {
    this.config = config;
    this.sessionId = this.generateSessionId();
    this.appVersion = this.getAppVersion();
    this.platform = this.getPlatform();
    
    // Load existing reports from storage
    this.loadLocalReports();
    
    // Set up global error handlers
    this.setupGlobalErrorHandlers();
  }

  /**
   * Report an error
   */
  async reportError(error: AppError): Promise<void> {
    const report: ErrorReport = {
      id: this.generateReportId(),
      timestamp: new Date(),
      error,
      sessionId: this.sessionId,
      appVersion: this.appVersion,
      platform: this.platform,
      resolved: false,
      reportedToRemote: false
    };

    // Store locally if enabled
    if (this.config.enableLocalLogging) {
      this.storeLocalReport(report);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      this.logToConsole(error);
    }

    // Report to remote service if enabled and user consented
    if (this.config.enableRemoteReporting && this.hasUserConsent()) {
      try {
        await this.reportToRemote(report);
        report.reportedToRemote = true;
      } catch (remoteError) {
        console.warn('Failed to report error to remote service:', remoteError);
      }
    }

    // Trigger error analytics
    this.trackErrorAnalytics(error);
  }

  /**
   * Get local error reports
   */
  getLocalReports(filters?: {
    severity?: ErrorSeverity;
    resolved?: boolean;
    dateRange?: { start: Date; end: Date };
  }): ErrorReport[] {
    let reports = [...this.localReports];

    if (filters) {
      if (filters.severity) {
        reports = reports.filter(r => r.error.severity === filters.severity);
      }
      if (filters.resolved !== undefined) {
        reports = reports.filter(r => r.resolved === filters.resolved);
      }
      if (filters.dateRange) {
        reports = reports.filter(r => 
          r.timestamp >= filters.dateRange!.start && 
          r.timestamp <= filters.dateRange!.end
        );
      }
    }

    return reports.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Mark an error as resolved
   */
  markAsResolved(reportId: string): void {
    const report = this.localReports.find(r => r.id === reportId);
    if (report) {
      report.resolved = true;
      this.saveLocalReports();
    }
  }

  /**
   * Clear old reports
   */
  clearOldReports(olderThanDays: number = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    this.localReports = this.localReports.filter(
      report => report.timestamp > cutoffDate
    );
    
    this.saveLocalReports();
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    total: number;
    bySeverity: Record<ErrorSeverity, number>;
    byCode: Record<string, number>;
    resolved: number;
    unresolved: number;
  } {
    const stats = {
      total: this.localReports.length,
      bySeverity: {
        [ErrorSeverity.LOW]: 0,
        [ErrorSeverity.MEDIUM]: 0,
        [ErrorSeverity.HIGH]: 0,
        [ErrorSeverity.CRITICAL]: 0
      },
      byCode: {} as Record<string, number>,
      resolved: 0,
      unresolved: 0
    };

    this.localReports.forEach(report => {
      // Count by severity
      stats.bySeverity[report.error.severity]++;
      
      // Count by error code
      const code = report.error.code;
      stats.byCode[code] = (stats.byCode[code] || 0) + 1;
      
      // Count resolved/unresolved
      if (report.resolved) {
        stats.resolved++;
      } else {
        stats.unresolved++;
      }
    });

    return stats;
  }

  /**
   * Export error reports for analysis
   */
  exportReports(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      return this.exportToCsv();
    }
    return JSON.stringify(this.localReports, null, 2);
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateReportId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getAppVersion(): string {
    // In a real app, this would come from package.json or build info
    return process.env.REACT_APP_VERSION || '1.0.0';
  }

  private getPlatform(): string {
    return navigator.platform || 'unknown';
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const context: ErrorContext = {
        component: 'global',
        action: 'unhandled_promise_rejection',
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      // Create AppError from the rejection
      const error = new AppError(
        {
          code: 'UNEXPECTED_ERROR' as any,
          severity: ErrorSeverity.HIGH,
          retryable: false,
          userMessage: 'An unexpected error occurred',
          technicalMessage: `Unhandled promise rejection: ${event.reason}`
        },
        context
      );

      this.reportError(error);
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      const context: ErrorContext = {
        component: 'global',
        action: 'javascript_error',
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        additionalData: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      };

      const error = new AppError(
        {
          code: 'UNEXPECTED_ERROR' as any,
          severity: ErrorSeverity.HIGH,
          retryable: false,
          userMessage: 'An unexpected error occurred',
          technicalMessage: event.message
        },
        context
      );

      this.reportError(error);
    });
  }

  private storeLocalReport(report: ErrorReport): void {
    this.localReports.push(report);
    
    // Maintain max reports limit
    if (this.localReports.length > this.config.maxLocalReports) {
      this.localReports = this.localReports
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, this.config.maxLocalReports);
    }
    
    this.saveLocalReports();
  }

  private loadLocalReports(): void {
    try {
      const stored = localStorage.getItem('flowdownload_error_reports');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.localReports = parsed.map((report: any) => ({
          ...report,
          timestamp: new Date(report.timestamp)
        }));
      }
    } catch (error) {
      console.warn('Failed to load local error reports:', error);
      this.localReports = [];
    }
  }

  private saveLocalReports(): void {
    try {
      localStorage.setItem(
        'flowdownload_error_reports',
        JSON.stringify(this.localReports)
      );
    } catch (error) {
      console.warn('Failed to save local error reports:', error);
    }
  }

  private async reportToRemote(report: ErrorReport): Promise<void> {
    if (!this.config.reportingEndpoint || !this.config.apiKey) {
      throw new Error('Remote reporting not configured');
    }

    const response = await fetch(this.config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        ...report,
        error: report.error.toJSON()
      })
    });

    if (!response.ok) {
      throw new Error(`Remote reporting failed: ${response.statusText}`);
    }
  }

  private hasUserConsent(): boolean {
    if (!this.config.enableUserConsent) {
      return true;
    }
    
    return localStorage.getItem('flowdownload_error_reporting_consent') === 'true';
  }

  private logToConsole(error: AppError): void {
    const logMethod = this.getConsoleMethod(error.severity);
    
    console.group(`🚨 ${error.severity.toUpperCase()} Error: ${error.code}`);
    console[logMethod]('User Message:', error.userMessage);
    console[logMethod]('Technical Message:', error.technicalMessage);
    console[logMethod]('Context:', error.context);
    if (error.originalError) {
      console[logMethod]('Original Error:', error.originalError);
    }
    console.groupEnd();
  }

  private getConsoleMethod(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }

  private trackErrorAnalytics(error: AppError): void {
    // In a real app, this would integrate with analytics services
    // For now, we'll just track basic metrics
    const event = {
      name: 'error_occurred',
      properties: {
        error_code: error.code,
        error_severity: error.severity,
        component: error.context.component,
        action: error.context.action,
        retryable: error.retryable
      }
    };
    
    // This would be sent to your analytics service
    console.debug('Error Analytics Event:', event);
  }

  private exportToCsv(): string {
    const headers = [
      'ID',
      'Timestamp',
      'Error Code',
      'Severity',
      'User Message',
      'Technical Message',
      'Component',
      'Action',
      'Resolved',
      'Reported to Remote'
    ];

    const rows = this.localReports.map(report => [
      report.id,
      report.timestamp.toISOString(),
      report.error.code,
      report.error.severity,
      `"${report.error.userMessage.replace(/"/g, '""')}"`,
      `"${report.error.technicalMessage.replace(/"/g, '""')}"`,
      report.error.context.component || '',
      report.error.context.action || '',
      report.resolved ? 'Yes' : 'No',
      report.reportedToRemote ? 'Yes' : 'No'
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }
}

// Singleton instance
let errorReportingService: ErrorReportingService | null = null;

/**
 * Initialize the error reporting service
 */
export function initializeErrorReporting(config: ErrorReportingConfig): ErrorReportingService {
  errorReportingService = new ErrorReportingService(config);
  return errorReportingService;
}

/**
 * Get the error reporting service instance
 */
export function getErrorReportingService(): ErrorReportingService {
  if (!errorReportingService) {
    throw new Error('Error reporting service not initialized');
  }
  return errorReportingService;
}

/**
 * Convenience function to report an error
 */
export async function reportError(error: AppError): Promise<void> {
  const service = getErrorReportingService();
  await service.reportError(error);
}