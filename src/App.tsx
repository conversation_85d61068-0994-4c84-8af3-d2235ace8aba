import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useDownloadStore } from './store/downloadStore';
import { ThemeProvider } from './ThemeContext';
import ErrorBoundary from './components/ErrorBoundary';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import RecentActivity from './components/RecentActivity';
import SystemStatus from './components/SystemStatus';
import QuickActions from './components/QuickActions';
import PluginManager from './components/PluginManager';
import Settings from './components/Settings';
import DownloadFolderDiagnosticComponent from './components/DownloadFolderDiagnostic';
import { initializeBuiltinPlugins } from './plugins';
import { configManager, getConfig } from './config/configManager';

function App() {
  const { isInitialized, initializeDefaultPath } = useDownloadStore();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isPluginManagerOpen, setIsPluginManagerOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isDiagnosticOpen, setIsDiagnosticOpen] = useState(false);
  const [config, setConfig] = useState(getConfig());

  useEffect(() => {
    if (!isInitialized) {
      initializeDefaultPath();
    }
  }, [isInitialized, initializeDefaultPath]);

  useEffect(() => {
    // Initialize configuration manager
    const initConfig = async () => {
      try {
        console.log('Initializing configuration...');
        await configManager.initialize();
        setConfig(getConfig());
        console.log('Configuration initialized successfully');
      } catch (error) {
        console.error('Configuration initialization failed:', error);
      }
    };

    // Initialize plugin system safely
    const initPlugins = async () => {
      try {
        console.log('Starting plugin initialization...');
        await initializeBuiltinPlugins();
        console.log('Plugin initialization completed');
      } catch (error) {
        console.error('Plugin initialization failed:', error);
      }
    };

    // Initialize configuration first, then plugins
    initConfig().then(() => {
      setTimeout(initPlugins, 1000);
    });
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-slate-900 text-gray-900 dark:text-gray-100">
          {/* Header */}
          <ErrorBoundary>
            <Header
              onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              onPluginManagerOpen={() => setIsPluginManagerOpen(true)}
              onSettingsOpen={() => setIsSettingsOpen(true)}
              onDiagnosticOpen={() => setIsDiagnosticOpen(true)}
            />
          </ErrorBoundary>
          
          {/* Main Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Sidebar - Hidden on mobile unless toggled */}
            <div className={`${isMobileMenuOpen ? 'block' : 'hidden'} md:block md:w-80 shrink-0 overflow-y-auto border-r border-gray-200 dark:border-gray-800`}>
              <ErrorBoundary>
                <Sidebar />
              </ErrorBoundary>
            </div>
            
            {/* Central area */}
            <div className="flex-1 flex flex-col overflow-hidden">
              <div className="flex-1 flex flex-col lg:flex-row">
                {/* Main area (downloads form) */}
                <div className="flex-1 p-4 overflow-y-auto">
                  <ErrorBoundary>
                    <MainContent />
                  </ErrorBoundary>
                </div>
                
                {/* Right sidebar */}
                <div className="w-full lg:w-80 shrink-0 p-4 border-t lg:border-t-0 lg:border-l border-gray-200 dark:border-gray-800 overflow-y-auto">
                  <div className="space-y-8">
                    <ErrorBoundary>
                      <RecentActivity />
                    </ErrorBoundary>
                    <ErrorBoundary>
                      <SystemStatus />
                    </ErrorBoundary>
                    <ErrorBoundary>
                      <QuickActions />
                    </ErrorBoundary>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <ToastContainer
            position="bottom-right"
            autoClose={config.ui.notifications ? 5000 : 0}
            hideProgressBar={false}
            closeOnClick
            pauseOnHover
            draggable
            theme="colored"
          />
          
          {/* Plugin Manager Modal */}
          <PluginManager
            isOpen={isPluginManagerOpen}
            onClose={() => setIsPluginManagerOpen(false)}
          />

          {/* Settings Modal */}
          <Settings
            isOpen={isSettingsOpen}
            onClose={() => setIsSettingsOpen(false)}
          />

          {/* Download Folder Diagnostic Modal */}
          <DownloadFolderDiagnosticComponent
            isOpen={isDiagnosticOpen}
            onClose={() => setIsDiagnosticOpen(false)}
            onFolderFixed={(path) => {
              console.log('Download folder fixed:', path);
              setConfig(getConfig()); // Refresh config
            }}
          />
        </div>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App; 