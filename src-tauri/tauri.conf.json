{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "FlowDownload", "version": "1.0.0", "identifier": "com.flowdownload.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:3456", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "FlowDownload Desktop Pro", "width": 1280, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true, "decorations": true}], "security": {"csp": null}}, "plugins": {"shell": {"open": true}, "dialog": null, "fs": null, "path": null}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["bin/*"]}}