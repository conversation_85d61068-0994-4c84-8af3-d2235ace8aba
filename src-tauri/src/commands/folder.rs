use std::path::{Path, PathBuf};
use tauri::command;

#[command]
pub fn get_downloads_folder() -> Result<String, String> {
    // Try multiple methods to get the downloads folder
    
    // Method 1: Use dirs crate (most reliable)
    if let Some(downloads_dir) = dirs::download_dir() {
        if downloads_dir.exists() && downloads_dir.is_dir() {
            // Verify we can actually write to it
            match test_directory_write_access(&downloads_dir) {
                Ok(true) => {
                    println!("✅ Downloads directory found and accessible: {:?}", downloads_dir);
                    return Ok(downloads_dir.to_string_lossy().to_string());
                }
                Ok(false) => {
                    println!("⚠️ Downloads directory exists but not writable: {:?}", downloads_dir);
                }
                Err(e) => {
                    println!("⚠️ Error testing downloads directory: {}", e);
                }
            }
        } else {
            println!("⚠️ Downloads directory from dirs crate doesn't exist: {:?}", downloads_dir);
        }
    }
    
    // Method 2: Try to create Downloads in home directory
    if let Some(home_dir) = dirs::home_dir() {
        let downloads_path = home_dir.join("Downloads");
        
        // Try to create it if it doesn't exist
        if !downloads_path.exists() {
            println!("📁 Creating Downloads directory: {:?}", downloads_path);
            if let Err(e) = std::fs::create_dir_all(&downloads_path) {
                println!("❌ Failed to create Downloads directory: {}", e);
            }
        }
        
        // Test if we can use it now
        if downloads_path.exists() && downloads_path.is_dir() {
            match test_directory_write_access(&downloads_path) {
                Ok(true) => {
                    println!("✅ Created/verified Downloads directory: {:?}", downloads_path);
                    return Ok(downloads_path.to_string_lossy().to_string());
                }
                Ok(false) => {
                    println!("⚠️ Downloads directory not writable: {:?}", downloads_path);
                }
                Err(e) => {
                    println!("⚠️ Error testing created Downloads directory: {}", e);
                }
            }
        }
    }
    
    // Method 3: Try Desktop as fallback
    if let Some(desktop_dir) = dirs::desktop_dir() {
        let downloads_path = desktop_dir.join("FlowDownload");
        
        if !downloads_path.exists() {
            println!("📁 Creating FlowDownload directory on Desktop: {:?}", downloads_path);
            if let Err(e) = std::fs::create_dir_all(&downloads_path) {
                println!("❌ Failed to create FlowDownload directory: {}", e);
            }
        }
        
        if downloads_path.exists() && downloads_path.is_dir() {
            match test_directory_write_access(&downloads_path) {
                Ok(true) => {
                    println!("✅ Using Desktop/FlowDownload directory: {:?}", downloads_path);
                    return Ok(downloads_path.to_string_lossy().to_string());
                }
                Ok(false) => {
                    println!("⚠️ Desktop/FlowDownload directory not writable: {:?}", downloads_path);
                }
                Err(e) => {
                    println!("⚠️ Error testing Desktop/FlowDownload directory: {}", e);
                }
            }
        }
    }
    
    // Method 4: Use Documents folder as fallback
    if let Some(documents_dir) = dirs::document_dir() {
        let downloads_path = documents_dir.join("FlowDownload");
        
        if !downloads_path.exists() {
            println!("📁 Creating FlowDownload directory in Documents: {:?}", downloads_path);
            if let Err(e) = std::fs::create_dir_all(&downloads_path) {
                println!("❌ Failed to create FlowDownload directory in Documents: {}", e);
            }
        }
        
        if downloads_path.exists() && downloads_path.is_dir() {
            match test_directory_write_access(&downloads_path) {
                Ok(true) => {
                    println!("✅ Using Documents/FlowDownload directory: {:?}", downloads_path);
                    return Ok(downloads_path.to_string_lossy().to_string());
                }
                Ok(false) => {
                    println!("⚠️ Documents/FlowDownload directory not writable: {:?}", downloads_path);
                }
                Err(e) => {
                    println!("⚠️ Error testing Documents/FlowDownload directory: {}", e);
                }
            }
        }
    }
    
    // Method 5: Current working directory as last resort
    let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    let downloads_path = current_dir.join("downloads");
    
    if !downloads_path.exists() {
        println!("📁 Creating downloads directory in current location: {:?}", downloads_path);
        if let Err(e) = std::fs::create_dir_all(&downloads_path) {
            return Err(format!("Failed to create downloads directory: {}", e));
        }
    }
    
    match test_directory_write_access(&downloads_path) {
        Ok(true) => {
            println!("✅ Using local downloads directory: {:?}", downloads_path);
            Ok(downloads_path.to_string_lossy().to_string())
        }
        Ok(false) => {
            Err("No writable directory found for downloads".to_string())
        }
        Err(e) => {
            Err(format!("Error testing local downloads directory: {}", e))
        }
    }
}

#[command]
pub fn ensure_directory_exists(path: String) -> Result<bool, String> {
    let path_buf = PathBuf::from(&path);
    
    // Check if it already exists
    if path_buf.exists() && path_buf.is_dir() {
        return test_directory_write_access(&path_buf);
    }
    
    // Try to create it
    println!("📁 Creating directory: {}", path);
    match std::fs::create_dir_all(&path_buf) {
        Ok(()) => {
            println!("✅ Successfully created directory: {}", path);
            test_directory_write_access(&path_buf)
        }
        Err(e) => {
            println!("❌ Failed to create directory {}: {}", path, e);
            Err(format!("Failed to create directory: {}", e))
        }
    }
}

#[command]
pub fn test_directory_access(path: String) -> Result<bool, String> {
    let path_buf = PathBuf::from(&path);
    test_directory_write_access(&path_buf)
}

fn test_directory_write_access(path: &Path) -> Result<bool, String> {
    if !path.exists() {
        return Err("Directory does not exist".to_string());
    }
    
    if !path.is_dir() {
        return Err("Path is not a directory".to_string());
    }
    
    // Test write access by creating a temporary file
    let test_file = path.join(".flowdownload_test");
    
    match std::fs::write(&test_file, b"test") {
        Ok(()) => {
            // Clean up test file
            let _ = std::fs::remove_file(&test_file);
            println!("✅ Directory is writable: {:?}", path);
            Ok(true)
        }
        Err(e) => {
            println!("❌ Directory is not writable: {:?}, error: {}", path, e);
            Ok(false)
        }
    }
}

#[command]
pub fn open_folder_in_explorer(path: String) -> Result<(), String> {
    let path_buf = PathBuf::from(&path);
    
    if !path_buf.exists() {
        return Err("Folder does not exist".to_string());
    }
    
    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg(&path)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }
    
    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(&path)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }
    
    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(&path)
            .spawn()
            .map_err(|e| format!("Failed to open folder: {}", e))?;
    }
    
    Ok(())
}

#[command]
pub fn resolve_path(path: String) -> Result<String, String> {
    let path_buf = PathBuf::from(&path);
    
    // Handle tilde expansion manually
    let expanded_path = if path.starts_with("~/") {
        if let Some(home_dir) = dirs::home_dir() {
            home_dir.join(&path[2..])
        } else {
            path_buf
        }
    } else {
        path_buf
    };
    
    // Try to canonicalize the path
    match expanded_path.canonicalize() {
        Ok(canonical_path) => Ok(canonical_path.to_string_lossy().to_string()),
        Err(_) => {
            // If canonicalization fails, return the expanded path
            Ok(expanded_path.to_string_lossy().to_string())
        }
    }
}