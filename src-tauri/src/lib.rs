#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  let downloads: Downloads = Arc::new(Mutex::new(HashMap::new()));
  
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
        
        // Log system information for debugging
        #[cfg(debug_assertions)]
        {
          println!("Application name: {}", env!("CARGO_PKG_NAME"));
          
          if let Ok(exe_dir) = std::env::current_exe() {
            println!("Executable path: {:?}", exe_dir);
          }
          
          if let Ok(cwd) = std::env::current_dir() {
            println!("Current working directory: {:?}", cwd);
          }
        }
      }
      
      // Make sure bundled yt-dlp is executable
      #[cfg(not(target_os = "windows"))]
      {
        use std::path::PathBuf;
        
        // In Tauri v2, we need to resolve resource paths differently
        let resource_dir = app.handle().path().resource_dir().unwrap_or_else(|_| PathBuf::from("./"));
        let yt_dlp_path = resource_dir.join("bin").join("yt-dlp");
        
        if yt_dlp_path.exists() {
          println!("Making bundled yt-dlp executable: {:?}", yt_dlp_path);
          
          // Use std::process::Command to chmod +x the file
          match Command::new("chmod")
            .arg("+x")
            .arg(yt_dlp_path.to_string_lossy().to_string())
            .output() 
          {
            Ok(output) => {
              if output.status.success() {
                println!("Successfully made yt-dlp executable");
              } else {
                if let Ok(stderr) = String::from_utf8(output.stderr) {
                  println!("Failed to make yt-dlp executable: {}", stderr);
                } else {
                  println!("Failed to make yt-dlp executable");
                }
              }
            },
            Err(e) => {
              println!("Error running chmod: {}", e);
            }
          }
        } else {
          println!("Bundled yt-dlp not found in resources: {:?}", yt_dlp_path);
        }
      }
      
      Ok(())
    })
    .manage(downloads)
    .plugin(tauri_plugin_dialog::init())
    .plugin(tauri_plugin_fs::init())
    .plugin(tauri_plugin_shell::init())
    .invoke_handler(tauri::generate_handler![
      download_file,
      cancel_download,
      create_directory,
      check_directory_access,
      get_download_dir,
      get_home_dir,
      get_app_local_data_dir,
      check_file_permissions
    ])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

use std::collections::HashMap;
use std::sync::Arc;
use std::path::Path;
use std::process::Command;
use tauri::{command, Window, State, Emitter, Manager};
use tokio::fs::File;
use tokio::io::AsyncWriteExt;
use tokio::sync::Mutex;

// Store active downloads using tokio::sync::Mutex for async compatibility
type Downloads = Arc<Mutex<HashMap<String, bool>>>; // download_id -> is_cancelled

#[derive(Clone, serde::Serialize)]
struct DownloadProgress {
  id: String,
  downloaded: u64,
  total: u64,
  speed: String,
  estimated_remaining: Option<String>, // Estimated time remaining in human readable format
  bytes_per_second: u64,              // Raw bytes per second for more accurate calculations
}

// Helper function to format human-readable time
fn format_duration(seconds: u64) -> String {
  if seconds < 60 {
    format!("{}s", seconds)
  } else if seconds < 3600 {
    format!("{}m {}s", seconds / 60, seconds % 60)
  } else {
    format!("{}h {}m", seconds / 3600, (seconds % 3600) / 60)
  }
}

// Helper commands to get system directories
#[command]
fn get_app_local_data_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  let app_data_dir = dirs::data_local_dir()
    .ok_or_else(|| "Could not get app data directory".to_string())?;
  
  println!("App data directory: {:?}", app_data_dir);
  Ok(app_data_dir.to_string_lossy().to_string())
}

#[command]
fn get_download_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  let download_dir = dirs::download_dir()
    .ok_or_else(|| "Could not get download directory".to_string())?;
  
  println!("Download directory: {:?}", download_dir);
  Ok(download_dir.to_string_lossy().to_string())
}

#[command]
fn get_home_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  let home_dir = dirs::home_dir()
    .ok_or_else(|| "Could not get home directory".to_string())?;
  
  println!("Home directory: {:?}", home_dir);
  Ok(home_dir.to_string_lossy().to_string())
}

// Helper function to check file permissions
#[command]
async fn check_file_permissions(path: String) -> Result<bool, String> {
  println!("Checking file permissions for: {}", path);
  
  // First check if the file exists
  match tokio::fs::metadata(&path).await {
    Ok(metadata) => {
      // Check if it's a file
      if metadata.is_file() {
        // Try to open it for reading
        match tokio::fs::OpenOptions::new().read(true).open(&path).await {
          Ok(_) => {
            println!("Can read file: {}", path);
            
            // Try to open it for writing
            match tokio::fs::OpenOptions::new().write(true).open(&path).await {
              Ok(_) => {
                println!("Can write to file: {}", path);
                Ok(true)
              },
              Err(e) => {
                println!("Cannot write to file {}: {}", path, e);
                Err(format!("Cannot write to file: {}", e))
              }
            }
          },
          Err(e) => {
            println!("Cannot read file {}: {}", path, e);
            Err(format!("Cannot read file: {}", e))
          }
        }
      } else {
        println!("Path exists but is not a file: {}", path);
        Err("Path exists but is not a file".to_string())
      }
    },
    Err(_) => {
      // If file doesn't exist, check if we can create it
      if let Some(parent) = Path::new(&path).parent() {
        let parent_str = parent.to_string_lossy().to_string();
        
        match tokio::fs::metadata(&parent_str).await {
          Ok(parent_metadata) => {
            if parent_metadata.is_dir() {
              // Try to create a test file in the parent directory
              let test_file_path = Path::new(&parent_str).join("flowdownload_test.tmp");
              match tokio::fs::File::create(&test_file_path).await {
                Ok(_) => {
                  // Clean up the test file
                  let _ = tokio::fs::remove_file(&test_file_path).await;
                  println!("Can create file in directory: {}", parent_str);
                  Ok(true)
                },
                Err(e) => {
                  println!("Cannot create file in directory {}: {}", parent_str, e);
                  Err(format!("Cannot create file in directory: {}", e))
                }
              }
            } else {
              println!("Parent path exists but is not a directory: {}", parent_str);
              Err("Parent path exists but is not a directory".to_string())
            }
          },
          Err(_) => {
            println!("Parent directory does not exist: {}", parent_str);
            Err("Parent directory does not exist".to_string())
          }
        }
      } else {
        println!("Cannot determine parent directory for: {}", path);
        Err("Cannot determine parent directory".to_string())
      }
    }
  }
}

// Helper function to check if we can create a directory
#[command]
async fn create_directory(path: String) -> Result<bool, String> {
  println!("Attempting to create directory: {}", path);
  
  // Check if the path exists
  let path_exists = tokio::fs::metadata(&path).await.is_ok();
  
  if path_exists {
    println!("Directory already exists: {}", path);
    // Try to create a test file to verify write permissions
    let test_file_path = Path::new(&path).join("flowdownload_test.tmp");
    match tokio::fs::File::create(&test_file_path).await {
      Ok(_) => {
        // Clean up the test file
        let _ = tokio::fs::remove_file(&test_file_path).await;
        println!("Have write permissions to directory: {}", path);
        Ok(true)
      }
      Err(e) => {
        println!("Cannot write to directory {}: {}", path, e);
        Err(format!("Cannot write to directory: {}", e))
      }
    }
  } else {
    // Try to create the directory
    match tokio::fs::create_dir_all(&path).await {
      Ok(_) => {
        println!("Successfully created directory: {}", path);
        
        // Verify we can write to it
        let test_file_path = Path::new(&path).join("flowdownload_test.tmp");
        match tokio::fs::File::create(&test_file_path).await {
          Ok(_) => {
            // Clean up the test file
            let _ = tokio::fs::remove_file(&test_file_path).await;
            println!("Have write permissions to created directory: {}", path);
            Ok(true)
          }
          Err(e) => {
            println!("Cannot write to created directory {}: {}", path, e);
            Err(format!("Cannot write to created directory: {}", e))
          }
        }
      }
      Err(e) => {
        println!("Failed to create directory {}: {}", path, e);
        Err(format!("Failed to create directory: {}", e))
      }
    }
  }
}

// Helper function to check if we have access to a directory
#[command]
async fn check_directory_access(path: String) -> Result<bool, String> {
  println!("Checking access to directory: {}", path);
  
  // First check if the directory exists
  if let Ok(metadata) = tokio::fs::metadata(&path).await {
    if metadata.is_dir() {
      println!("Directory exists: {}", path);
      
      // Try to create a test file
      let test_file_path = Path::new(&path).join("flowdownload_test.tmp");
      match tokio::fs::File::create(&test_file_path).await {
        Ok(_) => {
          // Clean up the test file
          let _ = tokio::fs::remove_file(&test_file_path).await;
          println!("Have write permissions to directory: {}", path);
          Ok(true)
        }
        Err(e) => {
          println!("Cannot write to directory {}: {}", path, e);
          Err(format!("Cannot write to directory: {}", e))
        }
      }
    } else {
      println!("Path exists but is not a directory: {}", path);
      Err("Path exists but is not a directory".to_string())
    }
  } else {
    println!("Directory does not exist: {}", path);
    
    // Try to create it
    match tokio::fs::create_dir_all(&path).await {
      Ok(_) => {
        println!("Created directory: {}", path);
        
        // Verify we can write to it
        let test_file_path = Path::new(&path).join("flowdownload_test.tmp");
        match tokio::fs::File::create(&test_file_path).await {
          Ok(_) => {
            // Clean up the test file
            let _ = tokio::fs::remove_file(&test_file_path).await;
            println!("Have write permissions to created directory: {}", path);
            Ok(true)
          }
          Err(e) => {
            println!("Cannot write to created directory {}: {}", path, e);
            // Try to clean up
            let _ = tokio::fs::remove_dir(&path).await;
            Err(format!("Cannot write to created directory: {}", e))
          }
        }
      }
      Err(e) => {
        println!("Could not create directory {}: {}", path, e);
        Err(format!("Could not create directory: {}", e))
      }
    }
  }
}

// Helper function to check if yt-dlp is installed or available
fn is_ytdlp_available(app_handle: &tauri::AppHandle) -> bool {
  // First check bundled version in our resources directory
  let resource_dir = match app_handle.path().resource_dir() {
    Ok(path) => path,
    Err(e) => {
      println!("Error getting resource dir: {}", e);
      return false;
    }
  };
  
  let bundled_path = resource_dir.join("bin").join("yt-dlp");
  if bundled_path.exists() {
    println!("Found bundled yt-dlp at: {:?}", bundled_path);
    return true;
  }
  
  // Check common locations
  let paths = vec![
    "/usr/local/bin/yt-dlp",
    "/opt/homebrew/bin/yt-dlp",
    "/usr/bin/yt-dlp",
    "/opt/local/bin/yt-dlp",
    "C:\\Program Files\\yt-dlp\\yt-dlp.exe",
    "C:\\yt-dlp\\yt-dlp.exe"
  ];
  
  for path in paths {
    if Path::new(path).exists() {
      println!("Found yt-dlp at: {}", path);
      return true;
    }
  }
  
  // Try using which command on Unix-like systems
  #[cfg(not(target_os = "windows"))]
  {
    match Command::new("which").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          println!("Found yt-dlp in PATH");
          return true;
        }
      },
      Err(_) => {}
    }
  }
  
  // Try using where command on Windows
  #[cfg(target_os = "windows")]
  {
    match Command::new("where").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          println!("Found yt-dlp in PATH");
          return true;
        }
      },
      Err(_) => {}
    }
  }
  
  // If we get here, yt-dlp is not available
  println!("yt-dlp not found");
  false
}

// Get the path to yt-dlp executable
fn get_ytdlp_path(app_handle: &tauri::AppHandle) -> Option<String> {
  // First check bundled version
  let resource_dir = match app_handle.path().resource_dir() {
    Ok(path) => path,
    Err(e) => {
      println!("Error getting resource dir: {}", e);
      return None;
    }
  };
  
  let bundled_path = resource_dir.join("bin").join("yt-dlp");
  if bundled_path.exists() {
    return Some(bundled_path.to_string_lossy().to_string());
  }
  
  // Check common locations
  let paths = vec![
    "/usr/local/bin/yt-dlp",
    "/opt/homebrew/bin/yt-dlp",
    "/usr/bin/yt-dlp",
    "/opt/local/bin/yt-dlp",
    "C:\\Program Files\\yt-dlp\\yt-dlp.exe",
    "C:\\yt-dlp\\yt-dlp.exe"
  ];
  
  for path in paths {
    if Path::new(path).exists() {
      return Some(path.to_string());
    }
  }
  
  // Try using which command on Unix-like systems
  #[cfg(not(target_os = "windows"))]
  {
    match Command::new("which").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          if let Ok(path) = String::from_utf8(output.stdout) {
            let path = path.trim();
            if !path.is_empty() {
              return Some(path.to_string());
            }
          }
        }
      },
      Err(_) => {}
    }
  }
  
  // Try using where command on Windows
  #[cfg(target_os = "windows")]
  {
    match Command::new("where").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          if let Ok(path) = String::from_utf8(output.stdout) {
            let path = path.trim();
            if !path.is_empty() {
              return Some(path.to_string());
            }
          }
        }
      },
      Err(_) => {}
    }
  }
  
  None
}

#[command]
async fn download_file(
  window: Window,
  downloads: State<'_, Downloads>,
  id: String,
  url: String,
  file_path: String,
) -> Result<(), String> {
  // Add to active downloads
  {
    let mut downloads_map = downloads.lock().await;
    downloads_map.insert(id.clone(), false);
  }

  // Log the file path for debugging
  println!("Attempting to download file to: {}", file_path);
  
  // Check if URL is from a social media platform requiring a specialized downloader
  let requires_ytdlp = url.contains("youtube.com") || url.contains("youtu.be") || 
                       url.contains("twitter.com") || url.contains("x.com") || 
                       url.contains("facebook.com") || url.contains("fb.com") || url.contains("fb.watch");
  
  if requires_ytdlp {
    // Check if yt-dlp is available
    let app_handle = window.app_handle();
    if !is_ytdlp_available(&app_handle) {
      let error_msg = "This URL requires yt-dlp which is not installed. Please install yt-dlp to download from this platform.";
      println!("{}", error_msg);
      let _ = window.emit("download-error", &id);
      
      // Remove from active downloads
      {
        let mut downloads_map = downloads.lock().await;
        downloads_map.remove(&id);
      }
      
      return Err(error_msg.to_string());
    }
    
    // Get the path to yt-dlp
    let ytdlp_path = match get_ytdlp_path(&app_handle) {
      Some(path) => path,
      None => {
        let error_msg = "Could not determine path to yt-dlp";
        println!("{}", error_msg);
        let _ = window.emit("download-error", &id);
        
        // Remove from active downloads
        {
          let mut downloads_map = downloads.lock().await;
          downloads_map.remove(&id);
        }
        
        return Err(error_msg.to_string());
      }
    };
    
    // Use std::process::Command to download the video
    println!("Starting yt-dlp download from URL: {}", url);
    
    // Make sure parent directory exists
    let path = Path::new(&file_path);
    if let Some(parent) = path.parent() {
      let parent_str = parent.to_string_lossy().to_string();
      match create_directory(parent_str).await {
        Ok(_) => println!("Parent directory is ready for download"),
        Err(e) => return Err(format!("Error with parent directory: {}", e)),
      }
    }
    
    // Execute yt-dlp with appropriate options
    let mut command = if cfg!(target_os = "windows") {
      let mut cmd = Command::new("cmd");
      cmd.args(["/C", &ytdlp_path]);
      cmd
    } else {
      Command::new(&ytdlp_path)
    };
    
    // Configure command arguments
    command
      .arg("--newline")              // Separate output lines for easier parsing
      .arg("--progress")             // Show progress
      .arg("--progress-template")    // Customize progress output
      .arg("download:[%(progress.downloaded_bytes)s/%(progress.total_bytes)s]:%(progress.eta)s:%(progress.speed)s")
      .arg("-o")                     // Output file name
      .arg(&file_path)
      .arg(&url);
    
    // Start the command
    match command.spawn() {
      Ok(mut child) => {
        // Report as downloading
        let _ = window.emit("download-progress", &DownloadProgress {
          id: id.clone(),
          downloaded: 0,
          total: 100,  // We don't know the exact size yet
          speed: "Starting...".to_string(),
          estimated_remaining: Some("Calculating...".to_string()),
          bytes_per_second: 0,
        });
        
        // Wait for the command to complete
        match child.wait() {
          Ok(status) => {
            if status.success() {
              println!("yt-dlp download completed successfully");
              
              // Report progress as completed
              let _ = window.emit("download-progress", &DownloadProgress {
                id: id.clone(),
                downloaded: 100,
                total: 100,
                speed: "Completed".to_string(),
                estimated_remaining: None,
                bytes_per_second: 0,
              });
              
              // Emit completion
              let _ = window.emit("download-complete", &id);
              
              // Remove from active downloads
              {
                let mut downloads_map = downloads.lock().await;
                downloads_map.remove(&id);
              }
              
              return Ok(());
            } else {
              let error_msg = format!("yt-dlp exited with status: {:?}", status);
              println!("{}", error_msg);
              let _ = window.emit("download-error", &id);
              
              // Remove from active downloads
              {
                let mut downloads_map = downloads.lock().await;
                downloads_map.remove(&id);
              }
              
              return Err(error_msg);
            }
          },
          Err(e) => {
            let error_msg = format!("Failed to wait for yt-dlp: {}", e);
            println!("{}", error_msg);
            let _ = window.emit("download-error", &id);
            
            // Remove from active downloads
            {
              let mut downloads_map = downloads.lock().await;
              downloads_map.remove(&id);
            }
            
            return Err(error_msg);
          }
        }
      },
      Err(e) => {
        let error_msg = format!("Failed to spawn yt-dlp: {}", e);
        println!("{}", error_msg);
        let _ = window.emit("download-error", &id);
        
        // Remove from active downloads
        {
          let mut downloads_map = downloads.lock().await;
          downloads_map.remove(&id);
        }
        
        return Err(error_msg);
      }
    }
  } else if url.contains("instagram.com") {
    let error_msg = "Instagram downloads require gallery-dl or instaloader which is not installed. Please install one of these tools to download Instagram content.";
    println!("{}", error_msg);
    let _ = window.emit("download-error", &id);
    
    // Remove from active downloads
    {
      let mut downloads_map = downloads.lock().await;
      downloads_map.remove(&id);
    }
    
    return Err(error_msg.to_string());
  }
  
  // Make sure the file_path is valid
  let path = Path::new(&file_path);
  if let Some(parent) = path.parent() {
    let parent_str = parent.to_string_lossy().to_string();
    println!("Ensuring parent directory exists: {}", parent_str);
    
    match create_directory(parent_str).await {
      Ok(_) => println!("Parent directory is ready for download"),
      Err(e) => return Err(format!("Error with parent directory: {}", e)),
    }
  } else {
    println!("No parent directory found for path: {}", file_path);
  }

  // Check if we have permission to write to the file
  match check_file_permissions(file_path.clone()).await {
    Ok(_) => println!("We have permission to write to the file"),
    Err(e) => {
      println!("File permission error: {}", e);
      return Err(format!("File permission error: {}", e));
    }
  }

  let client = reqwest::Client::new();
  
  // Get the file size first
  let response = client.head(&url).send().await
    .map_err(|e| format!("Failed to fetch URL info: {}", e))?;
  
  let total_size = response.content_length().unwrap_or(0);
  
  // Start the actual download
  let mut response = client.get(&url).send().await
    .map_err(|e| format!("Failed to start download: {}", e))?;
  
  if !response.status().is_success() {
    return Err(format!("Server returned status: {}", response.status()));
  }

  // Create the file
  let mut file = match File::create(&file_path).await {
    Ok(file) => file,
    Err(e) => {
      println!("Error creating file at {}: {}", file_path, e);
      return Err(format!("Failed to create file: {}", e));
    }
  };

  let mut downloaded = 0u64;
  let start_time = std::time::Instant::now();
  let mut last_update_time = start_time;
  let mut last_downloaded = 0u64;
  let update_interval = std::time::Duration::from_millis(500); // Update UI every 500ms
  
  // For calculating a smoother speed estimate
  let mut recent_speeds = Vec::with_capacity(5); // Store the last 5 speed measurements
  let mut bytes_per_second: u64;

  // Download with progress reporting
  while let Some(chunk) = response.chunk().await
    .map_err(|e| format!("Failed to read chunk: {}", e))? {
    
    // Check if download was cancelled
    {
      let downloads_map = downloads.lock().await;
      if downloads_map.get(&id).copied().unwrap_or(false) {
        // Clean up and return
        let _ = tokio::fs::remove_file(&file_path).await;
        return Err("Download cancelled".to_string());
      }
    }

    match file.write_all(&chunk).await {
      Ok(_) => {
        downloaded += chunk.len() as u64;
      },
      Err(e) => {
        println!("Error writing to file {}: {}", file_path, e);
        return Err(format!("Failed to write to file: {}", e));
      }
    }
    
    // Only update the UI periodically to avoid overwhelming it
    let now = std::time::Instant::now();
    if now.duration_since(last_update_time) >= update_interval {
      // Calculate current speed
      let elapsed_since_last = now.duration_since(last_update_time).as_secs_f64();
      let bytes_since_last = downloaded - last_downloaded;
      
      if elapsed_since_last > 0.0 {
        let current_speed = bytes_since_last as f64 / elapsed_since_last;
        
        // Add to recent speeds for smoother calculation
        recent_speeds.push(current_speed);
        if recent_speeds.len() > 5 {
          recent_speeds.remove(0);
        }
        
        // Calculate average speed from recent measurements
        let avg_speed = recent_speeds.iter().sum::<f64>() / recent_speeds.len() as f64;
        bytes_per_second = avg_speed as u64;
        
        // Calculate estimated time remaining
        let mut estimated_remaining = None;
        if total_size > 0 && bytes_per_second > 0 {
          let remaining_bytes = total_size - downloaded;
          let seconds_remaining = remaining_bytes as f64 / avg_speed;
          if seconds_remaining.is_finite() && seconds_remaining > 0.0 {
            estimated_remaining = Some(format_duration(seconds_remaining as u64));
          }
        }
        
        // Format human-readable speed
        let speed = if avg_speed > 1024.0 * 1024.0 {
          format!("{:.1} MB/s", avg_speed / (1024.0 * 1024.0))
        } else if avg_speed > 1024.0 {
          format!("{:.1} KB/s", avg_speed / 1024.0)
        } else {
          format!("{:.0} B/s", avg_speed)
        };

        // Emit progress using the Emitter trait
        let progress = DownloadProgress {
          id: id.clone(),
          downloaded,
          total: total_size,
          speed,
          estimated_remaining,
          bytes_per_second,
        };
        
        let _ = window.emit("download-progress", &progress);
        
        // Update for next interval
        last_update_time = now;
        last_downloaded = downloaded;
      }
    }
  }

  match file.flush().await {
    Ok(_) => println!("Successfully completed download to: {}", file_path),
    Err(e) => println!("Error flushing file {}: {}", file_path, e)
  }

  // Calculate overall stats for the completed download
  let total_elapsed = start_time.elapsed().as_secs_f64();
  let avg_speed = if total_elapsed > 0.0 {
    downloaded as f64 / total_elapsed
  } else {
    0.0
  };
  
  let final_speed = if avg_speed > 1024.0 * 1024.0 {
    format!("{:.1} MB/s", avg_speed / (1024.0 * 1024.0))
  } else if avg_speed > 1024.0 {
    format!("{:.1} KB/s", avg_speed / 1024.0)
  } else {
    format!("{:.0} B/s", avg_speed)
  };

  // Remove from active downloads
  {
    let mut downloads_map = downloads.lock().await;
    downloads_map.remove(&id);
  }

  // Emit final progress with accurate speed calculation
  let final_progress = DownloadProgress {
    id: id.clone(),
    downloaded,
    total: total_size,
    speed: final_speed,
    estimated_remaining: None,
    bytes_per_second: avg_speed as u64,
  };
  
  let _ = window.emit("download-progress", &final_progress);

  // Emit completion using the Emitter trait
  let _ = window.emit("download-complete", &id);
  
  Ok(())
}

#[command]
async fn cancel_download(
  downloads: State<'_, Downloads>,
  id: String,
) -> Result<(), String> {
  let mut downloads_map = downloads.lock().await;
  downloads_map.insert(id, true);
  Ok(())
}
